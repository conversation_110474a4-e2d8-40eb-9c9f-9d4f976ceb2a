<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - نظام المايكروسيرفس</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .register-container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .title {
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
            direction: ltr;
            text-align: left;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .register-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            margin-bottom: 20px;
        }

        .register-btn:hover {
            transform: translateY(-2px);
        }

        .register-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .login-link:hover {
            text-decoration: underline;
        }

        .alert {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            display: none;
            margin: 10px 0;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="logo">👤</div>
        <h1 class="title">إنشاء حساب جديد</h1>
        
        <div id="alert" class="alert" style="display: none;"></div>
        
        <form id="registerForm">
            <div class="form-group">
                <label for="name">الاسم الكامل:</label>
                <input type="text" id="name" name="name" required placeholder="أدخل اسمك الكامل">
            </div>
            
            <div class="form-group">
                <label for="email">البريد الإلكتروني:</label>
                <input type="email" id="email" name="email" required placeholder="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required placeholder="كلمة مرور قوية">
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">تأكيد كلمة المرور:</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required placeholder="أعد كتابة كلمة المرور">
            </div>
            
            <button type="submit" class="register-btn" id="registerBtn">
                إنشاء الحساب
            </button>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>جاري إنشاء الحساب...</p>
            </div>
        </form>
        
        <p>
            لديك حساب بالفعل؟ 
            <a href="login.html" class="login-link">تسجيل الدخول</a>
        </p>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001';
        
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const registerBtn = document.getElementById('registerBtn');
            const loading = document.getElementById('loading');
            
            // Validate passwords match
            if (password !== confirmPassword) {
                showAlert('كلمات المرور غير متطابقة', 'error');
                return;
            }
            
            // Show loading
            registerBtn.disabled = true;
            loading.style.display = 'block';
            hideAlert();
            
            try {
                const response = await fetch(`${API_BASE}/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'register',
                        name: name,
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    showAlert('تم إنشاء الحساب بنجاح! جاري التوجيه لتسجيل الدخول...', 'success');
                    
                    // Redirect to login after 2 seconds
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 2000);
                    
                } else {
                    showAlert('خطأ في إنشاء الحساب: ' + data.message, 'error');
                }
                
            } catch (error) {
                showAlert('خطأ في الاتصال: ' + error.message, 'error');
            } finally {
                registerBtn.disabled = false;
                loading.style.display = 'none';
            }
        });
        
        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert alert-${type}`;
            alert.style.display = 'block';
        }
        
        function hideAlert() {
            document.getElementById('alert').style.display = 'none';
        }
    </script>
</body>
</html>
