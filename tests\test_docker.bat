@echo off
title Docker Services Test
color 0A

echo.
echo 🐳 Docker Services Test
echo =======================
echo.

:: Check if Docker is running
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker not found or not running
    echo 💡 Please install Docker Desktop and make sure it's running
    pause
    exit /b 1
)

echo ✅ Docker is available
echo.

:: Check if services are running
echo 🔍 Checking Docker services...
docker-compose ps

echo.
echo 🧪 Testing service endpoints...
echo.

:: Wait a moment for services to be ready
echo ⏳ Waiting for services to be ready...
timeout /t 5 /nobreak >nul

:: Test API Gateway
echo 🌐 Testing API Gateway (Port 8000)...
curl -s http://localhost:8000/health
if %errorlevel% equ 0 (
    echo ✅ API Gateway: OK
) else (
    echo ❌ API Gateway: Failed
)
echo.

:: Test Auth Service
echo 🔐 Testing Auth Service (Port 8001)...
curl -s http://localhost:8001/health
if %errorlevel% equ 0 (
    echo ✅ Auth Service: OK
) else (
    echo ❌ Auth Service: Failed
)
echo.

:: Test User Service
echo 👤 Testing User Service (Port 8002)...
curl -s http://localhost:8002/health
if %errorlevel% equ 0 (
    echo ✅ User Service: OK
) else (
    echo ❌ User Service: Failed
)
echo.

:: Test Product Service
echo 🛍️ Testing Product Service (Port 8003)...
curl -s http://localhost:8003/health
if %errorlevel% equ 0 (
    echo ✅ Product Service: OK
) else (
    echo ❌ Product Service: Failed
)
echo.

:: Test Order Service
echo 📦 Testing Order Service (Port 8004)...
curl -s http://localhost:8004/health
if %errorlevel% equ 0 (
    echo ✅ Order Service: OK
) else (
    echo ❌ Order Service: Failed
)
echo.

:: Test Database Connection
echo 🗄️ Testing Database Connection...
docker-compose exec -T db mysql -u root -proot -e "SELECT 1" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Database: Connected
) else (
    echo ❌ Database: Connection failed
)
echo.

:: Test Redis Connection
echo 🔴 Testing Redis Connection...
docker-compose exec -T redis redis-cli ping >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Redis: Connected
) else (
    echo ❌ Redis: Connection failed
)
echo.

echo 🎉 Docker test completed!
echo.
echo 💡 If any services failed:
echo   - Check Docker Desktop is running
echo   - Run: docker-compose logs [service-name]
echo   - Try: docker-compose restart [service-name]
echo.
pause
