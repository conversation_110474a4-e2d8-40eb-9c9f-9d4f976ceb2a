# 🧠 نظام المايكروسيرفس الذكي

نظام ذكي يتحقق من وجود Docker أولاً، ثم ينتقل لـ XAMPP كبديل، مع تثبيت تلقائي كامل.

## ✨ الميزات الذكية

- 🔍 **كشف تلقائي للتقنيات** - يفحص Docker و XAMPP
- 🎯 **اختيار ذكي للطريقة** - Docker أولاً، ثم XAMPP
- 🚀 **تشغيل بخطوة واحدة** - RUN.bat يفعل كل شيء
- 🛑 **إيقاف ذكي** - STOP.bat يوقف أي طريقة تعمل
- 📊 **مراقبة شاملة** - STATUS.bat يعرض كل شيء

## 🎮 الاستخدام السريع

### تشغيل النظام (خطوة واحدة!)
```bash
# انقر مرتين على الملف
RUN.bat
```

### إيقاف النظام
```bash
# انقر مرتين على الملف
STOP.bat
```

### فحص حالة النظام
```bash
# انقر مرتين على الملف
STATUS.bat
```

## 🔍 كيف يعمل النظام الذكي

### 1. فحص التقنيات المتاحة
```
🔍 Checking for Docker...
✅ Docker: FOUND
✅ Docker Compose: FOUND

🔧 Checking for XAMPP...
✅ XAMPP: FOUND at C:\xampp\
```

### 2. اختيار الطريقة المناسبة
- **إذا وُجد Docker**: يُفضل Docker (أداء أفضل)
- **إذا وُجد XAMPP فقط**: يستخدم XAMPP
- **إذا لم يوجد شيء**: يعرض تحميل أحدهما

### 3. التشغيل التلقائي
- **مع Docker**: `docker-compose up -d`
- **مع XAMPP**: تشغيل خدمات PHP منفصلة

## 🐳 طريقة Docker (المُفضلة)

### المميزات
- ✅ **عزل كامل** - كل خدمة في container منفصل
- ✅ **سهولة التوسع** - يمكن تشغيل عدة نسخ
- ✅ **بيئة إنتاج** - مطابقة للخوادم الحقيقية
- ✅ **API Gateway** - نقطة دخول موحدة
- ✅ **Redis** - تخزين مؤقت وسريع

### المنافذ
- 🌐 **API Gateway**: http://localhost:8000
- 🔐 **Auth Service**: http://localhost:8001
- 👤 **User Service**: http://localhost:8002
- 🛍️ **Product Service**: http://localhost:8003
- 📦 **Order Service**: http://localhost:8004

### الأوامر المفيدة
```bash
# عرض حالة الخدمات
docker-compose ps

# عرض logs
docker-compose logs -f

# إعادة تشغيل خدمة معينة
docker-compose restart auth-service

# إيقاف كامل
docker-compose down
```

## 🔧 طريقة XAMPP (البديل البسيط)

### المميزات
- ✅ **بساطة التثبيت** - لا يحتاج Docker
- ✅ **وصول مباشر للملفات** - تعديل فوري
- ✅ **استهلاك ذاكرة أقل** - مناسب للأجهزة الضعيفة
- ✅ **تطوير سريع** - تغييرات فورية

### المنافذ
- 🔐 **Auth Service**: http://localhost:8001
- 👤 **User Service**: http://localhost:8002
- 🛍️ **Product Service**: http://localhost:8003
- 📦 **Order Service**: http://localhost:8004

### متطلبات XAMPP
- MySQL يجب أن يعمل في XAMPP Control Panel
- PHP 7.4+ مع extensions: pdo, pdo_mysql, curl, json

## 📊 مقارنة الطرق

| الميزة | Docker | XAMPP |
|--------|--------|-------|
| سهولة التثبيت | متوسط | سهل |
| الأداء | ممتاز | جيد |
| العزل | كامل | محدود |
| استهلاك الذاكرة | عالي | منخفض |
| التطوير | متوسط | سريع |
| الإنتاج | مثالي | مقبول |
| API Gateway | ✅ | ❌ |
| Redis | ✅ | ❌ |
| Load Balancing | ✅ | ❌ |

## 🧪 اختبار النظام

### اختبار شامل
```bash
# للنظام العام
STATUS.bat

# لـ Docker خصيصاً
tests\test_docker.bat

# لـ XAMPP خصيصاً
tests\api_test_xampp.php
```

### اختبار APIs يدوياً
```bash
# فحص صحة الخدمات
curl http://localhost:8001/health
curl http://localhost:8002/health
curl http://localhost:8003/health
curl http://localhost:8004/health

# مع Docker - فحص API Gateway
curl http://localhost:8000/health
```

## 🔄 التبديل بين الطرق

### من XAMPP إلى Docker
```bash
# أوقف XAMPP
STOP.bat

# احذف ملف الحالة
del .running_xampp

# شغل النظام مرة أخرى
RUN.bat
# سيختار Docker تلقائياً
```

### من Docker إلى XAMPP
```bash
# أوقف Docker
STOP.bat

# احذف ملف الحالة
del .running_docker

# شغل النظام مرة أخرى
RUN.bat
# سيعرض خيار الاختيار
```

## 🛠️ استكشاف الأخطاء

### مشاكل Docker
```bash
# تحقق من تشغيل Docker Desktop
docker --version

# تحقق من الخدمات
docker-compose ps

# عرض logs للأخطاء
docker-compose logs
```

### مشاكل XAMPP
```bash
# تحقق من MySQL
netstat -an | findstr :3306

# تحقق من PHP
C:\xampp\php\php.exe --version

# فحص العمليات
tasklist | findstr php.exe
```

### مشاكل عامة
```bash
# فحص شامل للنظام
STATUS.bat

# إعادة تثبيت
del .installed
RUN.bat
```

## 📁 ملفات النظام الذكي

### ملفات التشغيل
- `RUN.bat` - تشغيل ذكي
- `STOP.bat` - إيقاف ذكي  
- `STATUS.bat` - فحص الحالة

### ملفات الحالة
- `.running_docker` - Docker يعمل
- `.running_xampp` - XAMPP يعمل
- `.installed` - النظام مثبت

### ملفات التكوين
- `docker-compose.yml` - تكوين Docker
- `.env` - متغيرات البيئة
- `config.json` - إعدادات النظام

## 💡 نصائح للاستخدام الأمثل

### للمطورين
- استخدم **Docker** للتطوير الجاد
- استخدم **XAMPP** للتجارب السريعة
- راقب `STATUS.bat` بانتظام

### للإنتاج
- استخدم **Docker** دائماً
- فعّل `docker-compose.prod.yml`
- راقب logs باستمرار

### للتعلم
- ابدأ بـ **XAMPP** للبساطة
- انتقل لـ **Docker** عند الإتقان
- جرب التبديل بين الطرق

## 🎉 الخلاصة

النظام الذكي يوفر:
- 🔍 **كشف تلقائي** للتقنيات المتاحة
- 🎯 **اختيار مناسب** للطريقة الأفضل
- 🚀 **تشغيل بخطوة واحدة** بغض النظر عن الطريقة
- 📊 **مراقبة شاملة** لحالة النظام
- 🔄 **مرونة كاملة** في التبديل بين الطرق

**فقط شغل `RUN.bat` ودع النظام يختار الأفضل لك!** 🎯
