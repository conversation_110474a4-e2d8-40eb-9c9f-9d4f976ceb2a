<?php
/**
 * XAMPP Setup Script
 * Run this to setup the database for XAMPP environment
 */

echo "🚀 Setting up Microservices for XAMPP...\n\n";

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'microservices_db';

try {
    // Connect to MySQL
    echo "📡 Connecting to MySQL...\n";
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create database
    echo "🗄️ Creating database...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database");
    $pdo->exec("USE $database");

    // Read and execute init.sql
    echo "📋 Setting up tables...\n";
    $sql = file_get_contents('init.sql');

    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));

    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
            } catch (PDOException $e) {
                // Ignore duplicate entry errors for sample data
                if (strpos($e->getMessage(), 'Duplicate entry') === false) {
                    throw $e;
                }
            }
        }
    }

    echo "✅ Database setup completed!\n\n";

    // Test connection
    echo "🧪 Testing database connection...\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "👥 Users in database: " . $result['count'] . "\n";

    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "🛍️ Products in database: " . $result['count'] . "\n\n";

    echo "🎉 Setup completed successfully!\n";
    echo "📍 Next step: Run xampp_setup.bat to start the services\n";

} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "💡 Make sure XAMPP MySQL is running!\n";
    echo "💡 You can start MySQL from XAMPP Control Panel\n";
}
