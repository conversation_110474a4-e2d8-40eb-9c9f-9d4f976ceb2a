<?php
// ملف إنشاء الجداول مباشرة
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إنشاء الجداول</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }";
echo ".success { color: #27ae60; background: #d4edda; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo ".error { color: #e74c3c; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo ".info { color: #3498db; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";
echo "<h1>🛠️ إنشاء جداول قاعدة البيانات</h1>";

$host = 'localhost';
$dbname = 'company_management';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>✅ متصل بقاعدة البيانات: $dbname</div>";
    
    // إنشاء جدول المستخدمين
    echo "<div class='info'>📋 إنشاء جدول المستخدمين...</div>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            role ENUM('admin', 'manager', 'employee') DEFAULT 'employee',
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='success'>✅ تم إنشاء جدول المستخدمين</div>";
    
    // إنشاء جدول المشاريع
    echo "<div class='info'>📋 إنشاء جدول المشاريع...</div>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS projects (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            description TEXT,
            status ENUM('active', 'completed', 'pending', 'cancelled') DEFAULT 'pending',
            start_date DATE,
            end_date DATE,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='success'>✅ تم إنشاء جدول المشاريع</div>";
    
    // إنشاء جدول المهام
    echo "<div class='info'>📋 إنشاء جدول المهام...</div>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS tasks (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
            priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
            project_id INT,
            assigned_to INT,
            created_by INT,
            due_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='success'>✅ تم إنشاء جدول المهام</div>";
    
    // إنشاء جدول حالة الخدمات
    echo "<div class='info'>📋 إنشاء جدول حالة الخدمات...</div>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS system_services (
            id INT AUTO_INCREMENT PRIMARY KEY,
            service_name VARCHAR(100) NOT NULL,
            status ENUM('active', 'warning', 'inactive') DEFAULT 'active',
            last_check TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            response_time DECIMAL(5,2) DEFAULT 0.00,
            uptime_percentage DECIMAL(5,2) DEFAULT 100.00
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='success'>✅ تم إنشاء جدول حالة الخدمات</div>";
    
    // إنشاء جدول سجل النشاطات
    echo "<div class='info'>📋 إنشاء جدول سجل النشاطات...</div>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS activity_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            action VARCHAR(255) NOT NULL,
            user_id INT,
            user_name VARCHAR(100),
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='success'>✅ تم إنشاء جدول سجل النشاطات</div>";
    
    // إنشاء جدول مقاييس الأداء
    echo "<div class='info'>📋 إنشاء جدول مقاييس الأداء...</div>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS system_metrics (
            id INT AUTO_INCREMENT PRIMARY KEY,
            metric_name VARCHAR(100) NOT NULL,
            metric_value DECIMAL(10,2) NOT NULL,
            performance_score DECIMAL(5,2) DEFAULT 100.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<div class='success'>✅ تم إنشاء جدول مقاييس الأداء</div>";
    
    echo "<h2>📊 إدراج البيانات التجريبية</h2>";
    
    // إدراج المستخدمين
    echo "<div class='info'>👥 إدراج المستخدمين...</div>";
    $pdo->exec("
        INSERT IGNORE INTO users (username, email, password, full_name, role, status) VALUES
        ('admin', '<EMAIL>', '" . password_hash('password', PASSWORD_DEFAULT) . "', 'المدير العام', 'admin', 'active'),
        ('manager1', '<EMAIL>', '" . password_hash('password', PASSWORD_DEFAULT) . "', 'أحمد محمد', 'manager', 'active'),
        ('employee1', '<EMAIL>', '" . password_hash('password', PASSWORD_DEFAULT) . "', 'فاطمة علي', 'employee', 'active'),
        ('employee2', '<EMAIL>', '" . password_hash('password', PASSWORD_DEFAULT) . "', 'محمد سالم', 'employee', 'active'),
        ('employee3', '<EMAIL>', '" . password_hash('password', PASSWORD_DEFAULT) . "', 'نورا أحمد', 'employee', 'active')
    ");
    
    // إضافة 151 مستخدم إضافي للوصول إلى 156
    for ($i = 6; $i <= 156; $i++) {
        $username = 'user' . $i;
        $email = $username . '@company.com';
        $fullName = 'مستخدم رقم ' . $i;
        $role = ($i % 10 == 0) ? 'manager' : 'employee';
        $password = password_hash('password123', PASSWORD_DEFAULT);
        
        $pdo->exec("
            INSERT IGNORE INTO users (username, email, password, full_name, role, status) 
            VALUES ('$username', '$email', '$password', '$fullName', '$role', 'active')
        ");
    }
    echo "<div class='success'>✅ تم إدراج 156 مستخدم</div>";
    
    // إدراج المشاريع
    echo "<div class='info'>📋 إدراج المشاريع...</div>";
    $projects = [
        'تطوير موقع الشركة', 'نظام إدارة المخزون', 'تحديث النظام المحاسبي', 'تطبيق الجوال',
        'منصة التجارة الإلكترونية', 'نظام إدارة العملاء', 'تطبيق الموارد البشرية', 'نظام الأمان',
        'منصة التعلم الإلكتروني', 'تطبيق إدارة المشاريع', 'نظام إدارة المخازن', 'تطبيق خدمة العملاء',
        'منصة التواصل الداخلي', 'نظام التقارير المالية', 'تطبيق إدارة الوقت', 'نظام الجودة',
        'تطبيق الذكاء الاصطناعي', 'منصة التحليلات', 'نظام إدارة الطلبات', 'تطبيق الخدمات السحابية',
        'نظام إدارة المحتوى', 'تطبيق التسويق الرقمي', 'منصة البيانات الضخمة', 'نظام إدارة المخاطر'
    ];
    
    foreach ($projects as $index => $projectName) {
        $pdo->exec("
            INSERT IGNORE INTO projects (name, description, status, start_date, end_date, created_by) 
            VALUES ('$projectName', 'وصف تفصيلي للمشروع: $projectName', 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 6 MONTH), 1)
        ");
    }
    echo "<div class='success'>✅ تم إدراج 24 مشروع نشط</div>";
    
    // إدراج المهام المعلقة
    echo "<div class='info'>📝 إدراج المهام المعلقة...</div>";
    $tasks = [
        'مراجعة التصميم النهائي', 'اختبار الوحدة الجديدة', 'تحديث الوثائق', 'إعداد بيئة الاختبار',
        'مراجعة الكود المصدري', 'تحضير العرض التقديمي', 'تحليل متطلبات العميل', 'إعداد خطة المشروع'
    ];
    
    foreach ($tasks as $index => $taskTitle) {
        $pdo->exec("
            INSERT IGNORE INTO tasks (title, description, status, priority, project_id, assigned_to, created_by, due_date) 
            VALUES ('$taskTitle', 'وصف تفصيلي للمهمة: $taskTitle', 'pending', 'medium', 1, 2, 1, DATE_ADD(CURDATE(), INTERVAL 1 WEEK))
        ");
    }
    echo "<div class='success'>✅ تم إدراج 8 مهام معلقة</div>";
    
    // إدراج بيانات الخدمات
    echo "<div class='info'>🔧 إدراج بيانات الخدمات...</div>";
    $pdo->exec("
        INSERT IGNORE INTO system_services (service_name, status, last_check, response_time, uptime_percentage) VALUES
        ('خدمة المصادقة', 'active', NOW(), 0.15, 99.9),
        ('خدمة المستخدمين', 'active', NOW(), 0.23, 99.8),
        ('قاعدة البيانات', 'warning', DATE_SUB(NOW(), INTERVAL 3 MINUTE), 2.45, 98.5),
        ('خدمة النسخ الاحتياطي', 'active', DATE_SUB(NOW(), INTERVAL 30 SECOND), 0.12, 100.0),
        ('خدمة البريد الإلكتروني', 'active', NOW(), 0.89, 99.7)
    ");
    echo "<div class='success'>✅ تم إدراج بيانات الخدمات</div>";
    
    // إدراج سجل النشاطات
    echo "<div class='info'>📝 إدراج سجل النشاطات...</div>";
    $pdo->exec("
        INSERT IGNORE INTO activity_log (action, user_id, user_name, ip_address, created_at) VALUES
        ('تسجيل دخول مستخدم جديد', 2, 'أحمد محمد', '*************', DATE_SUB(NOW(), INTERVAL 5 MINUTE)),
        ('تحديث بيانات المشروع #123', 3, 'فاطمة علي', '*************', DATE_SUB(NOW(), INTERVAL 15 MINUTE)),
        ('إنشاء نسخة احتياطية', NULL, 'النظام', '127.0.0.1', DATE_SUB(NOW(), INTERVAL 30 MINUTE)),
        ('إضافة مستخدم جديد', 1, 'المدير العام', '*************', DATE_SUB(NOW(), INTERVAL 1 HOUR)),
        ('تحديث إعدادات النظام', 1, 'المدير العام', '*************', DATE_SUB(NOW(), INTERVAL 2 HOUR))
    ");
    echo "<div class='success'>✅ تم إدراج سجل النشاطات</div>";
    
    // إدراج مقاييس الأداء
    echo "<div class='info'>📊 إدراج مقاييس الأداء...</div>";
    $pdo->exec("
        INSERT IGNORE INTO system_metrics (metric_name, metric_value, performance_score, created_at) VALUES
        ('استخدام المعالج', 25.5, 99.2, NOW()),
        ('استخدام الذاكرة', 68.3, 98.8, NOW()),
        ('مساحة القرص الصلب', 45.2, 99.5, NOW()),
        ('سرعة الشبكة', 95.7, 99.9, NOW()),
        ('وقت الاستجابة', 0.25, 99.7, NOW())
    ");
    echo "<div class='success'>✅ تم إدراج مقاييس الأداء</div>";
    
    // التحقق النهائي
    echo "<h2>✅ التحقق النهائي</h2>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
    $userCount = $stmt->fetch()['count'];
    echo "<div class='success'>👥 المستخدمين النشطين: $userCount</div>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM projects WHERE status = 'active'");
    $projectCount = $stmt->fetch()['count'];
    echo "<div class='success'>📋 المشاريع النشطة: $projectCount</div>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM tasks WHERE status = 'pending'");
    $taskCount = $stmt->fetch()['count'];
    echo "<div class='success'>📝 المهام المعلقة: $taskCount</div>";
    
    echo "<h2>🎉 تم الانتهاء بنجاح!</h2>";
    echo "<div class='success'>";
    echo "<strong>✅ جميع الجداول والبيانات جاهزة!</strong><br><br>";
    echo "<a href='debug_api.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px;'>🔍 اختبار API</a>";
    echo "<a href='dashboard.html' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px;'>🚀 لوحة التحكم</a>";
    echo "</div>";
    
} catch(PDOException $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
}

echo "</div></body></html>";
?>
