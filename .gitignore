# Environment files
.env
.env.local
.env.production

# Logs
logs/
*.log
/var/log/

# Database
*.sql
backup_*.sql

# Docker
.docker/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Temporary files
/tmp/
/temp/
rate_limits/

# Node modules (if any)
node_modules/

# Composer (if any)
vendor/

# Cache
cache/
*.cache

# Build artifacts
build/
dist/

# Test results
test-results/
coverage/

# Local development
docker-compose.override.local.yml
