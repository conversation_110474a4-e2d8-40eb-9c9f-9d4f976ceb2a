<?php
echo "🧪 Testing All Microservices\n";
echo "============================\n\n";

$services = [
    'Auth Service' => 'http://localhost:8001/health',
    'User Service' => 'http://localhost:8002/health',
    'Product Service' => 'http://localhost:8003/health',
    'Order Service' => 'http://localhost:8004/health'
];

foreach ($services as $name => $url) {
    echo "🔍 Testing $name...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        if ($data && $data['status'] === 'healthy') {
            echo "✅ $name: HEALTHY\n";
        } else {
            echo "⚠️ $name: Response OK but not healthy\n";
            echo "   Response: $response\n";
        }
    } else {
        echo "❌ $name: FAILED (HTTP $httpCode)\n";
        if ($response) {
            echo "   Error: $response\n";
        }
    }
    echo "\n";
}

echo "🧪 Testing API functionality...\n";
echo "-------------------------------\n";

// Test product listing
echo "📦 Testing product listing...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8003/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    $data = json_decode($response, true);
    if ($data && $data['status'] === 'success') {
        echo "✅ Product listing: WORKING\n";
        echo "   Found " . count($data['data']) . " products\n";
    } else {
        echo "⚠️ Product listing: Unexpected response\n";
        echo "   Response: $response\n";
    }
} else {
    echo "❌ Product listing: FAILED (HTTP $httpCode)\n";
}

echo "\n🎉 Test completed!\n";
echo "\n💡 Next steps:\n";
echo "   1. All services should show as HEALTHY\n";
echo "   2. Try registering a user: POST to http://localhost:8001/\n";
echo "   3. Try logging in to get a token\n";
echo "   4. Use the token to access protected endpoints\n";
?>
