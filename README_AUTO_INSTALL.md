# 🚀 نظام التثبيت التلقائي للمايكروسيرفس

نظام تثبيت ذكي يقوم بإعداد وتكوين المايكروسيرفس تلقائياً في أول مرة تشغيل.

## ✨ الميزات

- 🔄 **تثبيت تلقائي كامل** - لا حاجة لخطوات يدوية
- 🔍 **كشف تلقائي لـ XAMPP** - يجد XAMPP في أي مكان
- 📥 **تحميل XAMPP تلقائياً** - إذا لم يكن مثبتاً
- 🗄️ **إعداد قاعدة البيانات** - جداول وبيانات تجريبية
- 🧪 **اختبار شامل** - يتأكد من سلامة التثبيت
- 🔗 **إنشاء اختصارات** - على سطح المكتب
- ⚙️ **ملفات تكوين** - تُنشأ تلقائياً

## 🎯 التشغيل السريع

### خطوة واحدة فقط!

```bash
# انقر مرتين على الملف
install.bat
```

**هذا كل شيء!** 🎉

## 📋 ما يحدث تلقائياً

### 1. فحص النظام
- ✅ البحث عن XAMPP
- ✅ فحص إصدار PHP
- ✅ التحقق من MySQL

### 2. تحميل المتطلبات (إذا لزم الأمر)
- 📥 تحميل XAMPP من الموقع الرسمي
- 🔧 تثبيت XAMPP تلقائياً
- ⚙️ تكوين الإعدادات

### 3. إعداد قاعدة البيانات
- 🗄️ إنشاء قاعدة البيانات
- 📋 إنشاء الجداول
- 📊 إدراج بيانات تجريبية
- 👤 إنشاء مستخدم إداري

### 4. تكوين النظام
- ⚙️ إنشاء ملفات التكوين
- 🔑 توليد مفاتيح JWT
- 📚 إنشاء وثائق API
- 🔗 إنشاء اختصارات سطح المكتب

### 5. اختبار التثبيت
- 🧪 اختبار الاتصال بقاعدة البيانات
- 🔍 فحص الملفات المطلوبة
- ✅ التحقق من العمليات الأساسية

### 6. تشغيل الخدمات
- 🚀 تشغيل جميع الخدمات تلقائياً
- 🌐 فتح المتصفح للاختبار
- 📱 عرض معلومات الوصول

## 🎮 الاستخدام بعد التثبيت

### تشغيل النظام
```bash
# انقر على الاختصار في سطح المكتب
"Start Microservices"

# أو شغل الملف مباشرة
start_services_direct.bat
```

### اختبار APIs
```bash
# انقر على الاختصار في سطح المكتب
"Test Microservices APIs"

# أو شغل الملف مباشرة
C:\xampp\php\php.exe tests/api_test_xampp.php
```

## 📍 معلومات الوصول

### الخدمات
- 🔐 **Auth Service**: http://localhost:8001
- 👤 **User Service**: http://localhost:8002
- 🛍️ **Product Service**: http://localhost:8003
- 📦 **Order Service**: http://localhost:8004

### المستخدم الإداري
- 📧 **Email**: <EMAIL>
- 🔑 **Password**: admin123

### قاعدة البيانات
- 🗄️ **Database**: microservices_db
- 👤 **User**: root
- 🔑 **Password**: (فارغ)

## 🔧 الملفات المُنشأة

### ملفات التكوين
- `.installed` - علامة التثبيت
- `.config` - إعدادات النظام
- `.env` - متغيرات البيئة
- `config.json` - تكوين JSON
- `api_docs.json` - وثائق API

### اختصارات سطح المكتب
- `Start Microservices.lnk` - تشغيل النظام
- `Test Microservices APIs.lnk` - اختبار APIs

## 🔄 إعادة التثبيت

إذا كنت تريد إعادة التثبيت:

```bash
# احذف ملف التثبيت
del .installed

# شغل المثبت مرة أخرى
install.bat
```

## 🛠️ استكشاف الأخطاء

### مشكلة: "XAMPP not found"
**الحل التلقائي:**
- المثبت سيعرض تحميل XAMPP تلقائياً
- اختر "y" للتحميل والتثبيت

**الحل اليدوي:**
1. حمل XAMPP من: https://www.apachefriends.org/
2. ثبته في `C:\xampp\`
3. أعد تشغيل `install.bat`

### مشكلة: "MySQL not running"
**الحل التلقائي:**
- المثبت سيحاول تشغيل MySQL تلقائياً

**الحل اليدوي:**
1. افتح XAMPP Control Panel
2. اضغط "Start" بجانب MySQL
3. أعد تشغيل `install.bat`

### مشكلة: "Installation test failed"
**الحل:**
1. تحقق من رسائل الخطأ
2. تأكد من تشغيل MySQL
3. تحقق من صلاحيات الملفات
4. أعد تشغيل `install.bat`

## 📊 معلومات النظام

### المتطلبات
- ✅ Windows 7/8/10/11
- ✅ 2GB RAM (الحد الأدنى)
- ✅ 1GB مساحة فارغة
- ✅ اتصال إنترنت (للتحميل)

### التقنيات المستخدمة
- 🐘 **PHP 8.1+**
- 🗄️ **MySQL 8.0**
- 🔑 **JWT Authentication**
- 🌐 **RESTful APIs**
- 🐳 **Docker Ready** (اختياري)

## 🎉 الميزات المتقدمة

### تحديث تلقائي
- النظام يتحقق من التحديثات
- يمكن تحديث الخدمات بدون إعادة تثبيت

### مراقبة الأداء
- Health checks تلقائية
- Logging مدمج
- إحصائيات الاستخدام

### أمان محسن
- JWT tokens آمنة
- Rate limiting
- Input validation

## 💡 نصائح للاستخدام

1. **استخدم Postman** لاختبار APIs
2. **راجع api_docs.json** للوثائق
3. **احفظ tokens** للاستخدام المتكرر
4. **راقب نوافذ الخدمات** للأخطاء
5. **استخدم phpMyAdmin** لإدارة قاعدة البيانات

## 🤝 الدعم

إذا واجهت مشاكل:
1. راجع ملف `QUICK_START.md`
2. تحقق من logs في نوافذ الخدمات
3. جرب إعادة التثبيت
4. تأكد من إصدار PHP (7.4+)
