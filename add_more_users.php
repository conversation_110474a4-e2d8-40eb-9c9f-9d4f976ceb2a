<?php
// ملف إضافة مستخدمين إضافيين لقاعدة البيانات
echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إضافة مستخدمين إضافيين</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #27ae60; background: #d4edda; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".error { color: #e74c3c; background: #f8d7da; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".info { color: #3498db; background: #d1ecf1; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo "h1 { color: #2c3e50; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";
echo "<h1>👥 إضافة مستخدمين إضافيين</h1>";

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'company_management';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // التحقق من العدد الحالي
    $stmt = $pdo->query("SELECT COUNT(*) as current_count FROM users WHERE status = 'active'");
    $currentCount = $stmt->fetch()['current_count'];
    
    echo "<div class='info'>📊 العدد الحالي للمستخدمين النشطين: $currentCount</div>";
    
    $targetCount = 156;
    $usersToAdd = $targetCount - $currentCount;
    
    if ($usersToAdd <= 0) {
        echo "<div class='success'>✅ العدد المطلوب ($targetCount) موجود بالفعل أو أكثر!</div>";
    } else {
        echo "<div class='info'>🎯 المطلوب إضافة: $usersToAdd مستخدم للوصول إلى $targetCount</div>";
        
        // قائمة أسماء عربية للمستخدمين
        $arabicNames = [
            'أحمد محمد', 'فاطمة علي', 'محمد سالم', 'نورا أحمد', 'علي حسن',
            'مريم خالد', 'يوسف عبدالله', 'سارة محمود', 'عمر إبراهيم', 'ليلى حسام',
            'حسن عبدالرحمن', 'زينب طارق', 'كريم فؤاد', 'هدى سعد', 'طارق عماد',
            'رنا وليد', 'سامي نبيل', 'دينا أشرف', 'ماجد فاروق', 'ندى رامي',
            'وائل صلاح', 'منى جمال', 'باسم رضا', 'هالة نادر', 'رامي شريف',
            'لمياء كمال', 'عادل منير', 'ريم سامر', 'فادي نصر', 'غادة عصام',
            'نبيل حكيم', 'سلمى فهد', 'جمال رشيد', 'إيمان صابر', 'شريف وسام',
            'نهى عاطف', 'مصطفى رفيق', 'هبة ماهر', 'خالد سليم', 'رشا عادل',
            'عبدالله نور', 'سمر حاتم', 'إسلام فتحي', 'نادية عمرو', 'حاتم زياد',
            'ياسمين فايز', 'عصام توفيق', 'رحاب سمير', 'أيمن صفوت', 'منال حازم'
        ];
        
        $domains = ['company.com', 'business.com', 'work.com', 'office.com', 'corp.com'];
        $roles = ['employee', 'manager', 'employee', 'employee', 'manager'];
        
        $addedCount = 0;
        
        for ($i = 0; $i < $usersToAdd; $i++) {
            $name = $arabicNames[$i % count($arabicNames)];
            $username = 'user' . (1000 + $i);
            $email = $username . '@' . $domains[$i % count($domains)];
            $role = $roles[$i % count($roles)];
            $password = password_hash('password123', PASSWORD_DEFAULT);
            
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, email, password, full_name, role, status, created_at) 
                    VALUES (?, ?, ?, ?, ?, 'active', NOW())
                ");
                
                $stmt->execute([$username, $email, $password, $name, $role]);
                $addedCount++;
                
                if ($addedCount % 20 == 0) {
                    echo "<div class='info'>📈 تم إضافة $addedCount مستخدم...</div>";
                }
                
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                    // تجاهل المستخدمين المكررين
                    continue;
                } else {
                    echo "<div class='error'>❌ خطأ في إضافة المستخدم $name: " . $e->getMessage() . "</div>";
                }
            }
        }
        
        echo "<div class='success'>✅ تم إضافة $addedCount مستخدم جديد بنجاح!</div>";
    }
    
    // التحقق من العدد النهائي
    $stmt = $pdo->query("SELECT COUNT(*) as final_count FROM users WHERE status = 'active'");
    $finalCount = $stmt->fetch()['final_count'];
    
    echo "<div class='success'>📊 العدد النهائي للمستخدمين النشطين: $finalCount</div>";
    
    // إضافة بعض المشاريع والمهام إذا لزم الأمر
    $stmt = $pdo->query("SELECT COUNT(*) as project_count FROM projects WHERE status = 'active'");
    $projectCount = $stmt->fetch()['project_count'];
    
    if ($projectCount < 24) {
        $projectsToAdd = 24 - $projectCount;
        echo "<div class='info'>📋 إضافة $projectsToAdd مشروع إضافي...</div>";
        
        $projectNames = [
            'تطوير تطبيق المبيعات', 'نظام إدارة العملاء', 'موقع التجارة الإلكترونية',
            'تطبيق الموارد البشرية', 'نظام المحاسبة المتقدم', 'منصة التعلم الإلكتروني',
            'تطبيق إدارة المشاريع', 'نظام إدارة المخازن', 'تطبيق خدمة العملاء',
            'منصة التواصل الداخلي', 'نظام التقارير المالية', 'تطبيق إدارة الوقت',
            'نظام الأمان والحماية', 'تطبيق التسويق الرقمي', 'منصة البيانات الضخمة',
            'نظام إدارة الجودة', 'تطبيق الذكاء الاصطناعي', 'منصة التحليلات',
            'نظام إدارة الطلبات', 'تطبيق الخدمات السحابية'
        ];
        
        for ($i = 0; $i < $projectsToAdd && $i < count($projectNames); $i++) {
            $stmt = $pdo->prepare("
                INSERT INTO projects (name, description, status, start_date, end_date, created_by, created_at) 
                VALUES (?, ?, 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 6 MONTH), 1, NOW())
            ");
            
            $stmt->execute([
                $projectNames[$i], 
                'وصف تفصيلي للمشروع: ' . $projectNames[$i]
            ]);
        }
        
        echo "<div class='success'>✅ تم إضافة المشاريع الإضافية!</div>";
    }
    
    // إضافة مهام معلقة
    $stmt = $pdo->query("SELECT COUNT(*) as task_count FROM tasks WHERE status = 'pending'");
    $taskCount = $stmt->fetch()['task_count'];
    
    if ($taskCount < 8) {
        $tasksToAdd = 8 - $taskCount;
        echo "<div class='info'>📝 إضافة $tasksToAdd مهمة معلقة...</div>";
        
        $taskTitles = [
            'مراجعة التصميم النهائي', 'اختبار الوحدة الجديدة', 'تحديث الوثائق',
            'إعداد بيئة الاختبار', 'مراجعة الكود المصدري', 'تحضير العرض التقديمي',
            'تحليل متطلبات العميل', 'إعداد خطة المشروع'
        ];
        
        for ($i = 0; $i < $tasksToAdd && $i < count($taskTitles); $i++) {
            $stmt = $pdo->prepare("
                INSERT INTO tasks (title, description, status, priority, project_id, assigned_to, created_by, due_date, created_at) 
                VALUES (?, ?, 'pending', 'medium', 1, 2, 1, DATE_ADD(CURDATE(), INTERVAL 1 WEEK), NOW())
            ");
            
            $stmt->execute([
                $taskTitles[$i], 
                'وصف تفصيلي للمهمة: ' . $taskTitles[$i]
            ]);
        }
        
        echo "<div class='success'>✅ تم إضافة المهام المعلقة!</div>";
    }
    
    echo "<h2>🎉 تم الانتهاء بنجاح!</h2>";
    echo "<div class='success'>";
    echo "<strong>📊 الإحصائيات النهائية:</strong><br>";
    echo "• المستخدمين النشطين: $finalCount<br>";
    echo "• المشاريع النشطة: " . $pdo->query("SELECT COUNT(*) FROM projects WHERE status = 'active'")->fetchColumn() . "<br>";
    echo "• المهام المعلقة: " . $pdo->query("SELECT COUNT(*) FROM tasks WHERE status = 'pending'")->fetchColumn() . "<br><br>";
    echo "<a href='dashboard.html' style='background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🚀 انتقل إلى لوحة التحكم</a>";
    echo "</div>";
    
} catch(PDOException $e) {
    echo "<div class='error'>";
    echo "<strong>❌ خطأ في الاتصال بقاعدة البيانات:</strong><br>";
    echo $e->getMessage() . "<br><br>";
    echo "<strong>🔧 تأكد من:</strong><br>";
    echo "• تشغيل خادم MySQL<br>";
    echo "• وجود قاعدة البيانات company_management<br>";
    echo "• تشغيل setup_database.php أولاً<br>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
