@echo off
title Microservices Setup for XAMPP
color 0B

echo.
echo 🚀 Microservices Setup for XAMPP
echo ================================
echo.

:: Common XAMPP PHP paths
set PHP_PATH=""
if exist "C:\xampp\php\php.exe" set PHP_PATH="C:\xampp\php\php.exe"
if exist "C:\XAMPP\php\php.exe" set PHP_PATH="C:\XAMPP\php\php.exe"
if exist "%USERPROFILE%\xampp\php\php.exe" set PHP_PATH="%USERPROFILE%\xampp\php\php.exe"

:: Check if PHP found
if %PHP_PATH%=="" (
    echo ❌ PHP not found in common XAMPP locations!
    echo.
    echo 💡 Please check your XAMPP installation:
    echo    - C:\xampp\php\php.exe
    echo    - C:\XAMPP\php\php.exe
    echo    - %USERPROFILE%\xampp\php\php.exe
    echo.
    echo 🔧 Or add PHP to your PATH environment variable
    pause
    exit /b 1
)

echo ✅ Found PHP at: %PHP_PATH%
echo.

:: Check MySQL connection
echo 🔍 Checking MySQL connection...
netstat -an | findstr :3306 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL not running on port 3306!
    echo.
    echo 💡 Please start MySQL in XAMPP Control Panel:
    echo    1. Open XAMPP Control Panel
    echo    2. Click 'Start' next to MySQL
    echo    3. Wait for it to turn green
    echo    4. Run this script again
    echo.
    pause
    exit /b 1
)
echo ✅ MySQL is running on port 3306
echo.

:: Setup database
echo 🗄️ Setting up database...
%PHP_PATH% setup_xampp.php
if %errorlevel% neq 0 (
    echo.
    echo ❌ Database setup failed!
    echo 💡 Check the error messages above
    pause
    exit /b 1
)

echo.
echo ✅ Database setup completed!
echo.
echo 🚀 Ready to start services!
echo.
echo 📍 Next steps:
echo    1. Run: start_services_direct.bat
echo    2. Or manually start each service:
echo       %PHP_PATH% -S localhost:8001 -t auth-service
echo       %PHP_PATH% -S localhost:8002 -t user-service
echo       %PHP_PATH% -S localhost:8003 -t product-service
echo       %PHP_PATH% -S localhost:8004 -t order-service
echo.
pause
