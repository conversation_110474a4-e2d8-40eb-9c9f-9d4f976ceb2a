<?php
class ProductController {
    private $db;
    private $product;

    public function __construct() {
        $database = new Database();
        $this->db = $database->connect();
        $this->product = new Product($this->db);
    }

    public function create($data) {
        $result = $this->product->create($data);
        if ($result) {
            return json_encode(['status' => 'success', 'message' => 'Product created successfully', 'id' => $result]);
        }
        return json_encode(['status' => 'error', 'message' => 'Failed to create product']);
    }

    public function getProduct($id) {
        $product = $this->product->getById($id);
        if ($product) {
            return json_encode(['status' => 'success', 'data' => $product]);
        }
        return json_encode(['status' => 'error', 'message' => 'Product not found']);
    }

    public function updateProduct($id, $data) {
        if ($this->product->update($id, $data)) {
            return json_encode(['status' => 'success', 'message' => 'Product updated successfully']);
        }
        return json_encode(['status' => 'error', 'message' => 'Failed to update product']);
    }

    public function deleteProduct($id) {
        if ($this->product->delete($id)) {
            return json_encode(['status' => 'success', 'message' => 'Product deleted successfully']);
        }
        return json_encode(['status' => 'error', 'message' => 'Failed to delete product']);
    }

    public function listProducts($page = 1, $limit = 10, $category = null) {
        $products = $this->product->getAll($page, $limit, $category);
        return json_encode(['status' => 'success', 'data' => $products]);
    }

    public function updateStock($id, $quantityChange) {
        if ($this->product->updateStock($id, $quantityChange)) {
            return json_encode(['status' => 'success', 'message' => 'Stock updated successfully']);
        }
        return json_encode(['status' => 'error', 'message' => 'Failed to update stock']);
    }
}
