<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام الحماية المتقدم</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap');
        
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --glass: rgba(255, 255, 255, 0.1);
            --shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            position: relative;
            overflow: hidden;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(120deg); }
            66% { transform: translateY(30px) rotate(240deg); }
        }

        .login-container {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 50px;
            border-radius: 30px;
            box-shadow: var(--shadow);
            width: 100%;
            max-width: 450px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            border-radius: 30px;
            z-index: -1;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: var(--primary);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            margin: 0 auto 20px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .title {
            color: #ffffff;
            margin-bottom: 10px;
            font-size: 32px;
            font-weight: 900;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 40px;
            font-size: 16px;
            font-weight: 400;
        }

        .form-group {
            margin-bottom: 25px;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 10px;
            color: #ffffff;
            font-weight: 600;
            font-size: 14px;
        }

        .input-wrapper {
            position: relative;
        }

        .form-group input {
            width: 100%;
            padding: 18px 20px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            font-size: 16px;
            transition: all 0.3s ease;
            direction: ltr;
            text-align: left;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            backdrop-filter: blur(10px);
            font-family: 'Cairo', sans-serif;
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-group input:focus {
            outline: none;
            border-color: rgba(79, 172, 254, 0.8);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 25px rgba(79, 172, 254, 0.3);
            transform: translateY(-2px);
        }

        .login-btn {
            width: 100%;
            padding: 18px;
            background: var(--success);
            color: #ffffff;
            border: none;
            border-radius: 15px;
            font-size: 18px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 25px;
            position: relative;
            overflow: hidden;
            font-family: 'Cairo', sans-serif;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(79, 172, 254, 0.4);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .register-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .register-link:hover {
            color: #4facfe;
            text-decoration: underline;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            font-weight: 600;
            border-right: 4px solid;
        }

        .alert-success {
            background: rgba(67, 233, 123, 0.2);
            border-right-color: #43e97b;
            color: #43e97b;
        }

        .alert-error {
            background: rgba(250, 112, 154, 0.2);
            border-right-color: #fa709a;
            color: #fa709a;
        }

        .loading {
            display: none;
            margin: 15px 0;
        }

        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .demo-users {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 15px;
            margin-top: 25px;
            text-align: right;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .demo-users h4 {
            color: #ffffff;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .demo-user {
            background: rgba(255, 255, 255, 0.1);
            padding: 12px 15px;
            margin: 8px 0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .demo-user:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .login-container {
                margin: 20px;
                padding: 30px;
            }
            
            .title {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">🛡️</div>
        <h1 class="title">نظام الحماية المتقدم</h1>
        <p class="subtitle">تسجيل دخول آمن ومشفر</p>
        
        <div id="alert" class="alert" style="display: none;"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">البريد الإلكتروني:</label>
                <div class="input-wrapper">
                    <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                </div>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <div class="input-wrapper">
                    <input type="password" id="password" name="password" required placeholder="••••••••">
                </div>
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">
                🔐 تسجيل الدخول
            </button>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>جاري تسجيل الدخول...</p>
            </div>
        </form>
        
        <p>
            ليس لديك حساب؟ 
            <a href="register.html" class="register-link">إنشاء حساب جديد</a>
        </p>
        
        <div class="demo-users">
            <h4>🔑 حسابات تجريبية:</h4>
            <div class="demo-user" onclick="fillLogin('<EMAIL>', 'password')">
                👨‍💼 المدير: <EMAIL>
            </div>
            <div class="demo-user" onclick="fillLogin('<EMAIL>', 'password')">
                👤 مستخدم: <EMAIL>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001';
        
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loading = document.getElementById('loading');
            
            // Show loading
            loginBtn.disabled = true;
            loading.style.display = 'block';
            hideAlert();
            
            try {
                const response = await fetch(`${API_BASE}/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    // Save token
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('refreshToken', data.refresh_token);
                    localStorage.setItem('userId', data.user_id);
                    
                    showAlert('✅ تم تسجيل الدخول بنجاح! جاري التوجيه...', 'success');
                    
                    // Redirect to modern dashboard after 2 seconds
                    setTimeout(() => {
                        window.location.href = 'modern_dashboard.html';
                    }, 2000);
                    
                } else {
                    showAlert('❌ خطأ في تسجيل الدخول: ' + data.message, 'error');
                }
                
            } catch (error) {
                showAlert('❌ خطأ في الاتصال: ' + error.message, 'error');
            } finally {
                loginBtn.disabled = false;
                loading.style.display = 'none';
            }
        });
        
        function fillLogin(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
            
            // Add visual feedback
            const inputs = document.querySelectorAll('input');
            inputs.forEach(input => {
                input.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    input.style.transform = 'scale(1)';
                }, 200);
            });
        }
        
        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert alert-${type}`;
            alert.style.display = 'block';
        }
        
        function hideAlert() {
            document.getElementById('alert').style.display = 'none';
        }
        
        // Check if already logged in
        if (localStorage.getItem('authToken')) {
            showAlert('🎉 أنت مسجل دخول بالفعل، جاري التوجيه...', 'success');
            setTimeout(() => {
                window.location.href = 'modern_dashboard.html';
            }, 1000);
        }

        // Add some interactive effects
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'translateY(-2px)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
