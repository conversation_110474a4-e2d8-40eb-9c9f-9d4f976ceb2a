# 🚀 دليل التشغيل السريع

## المشكلة الحالية
- <PERSON><PERSON> <PERSON>ير مثبت على النظام
- نحتاج لتشغيل النظام باستخدام XAMPP

## ✅ الحل السريع

### 1. تأكد من تشغيل XAMPP MySQL
1. افتح **XAMPP Control Panel**
2. اضغط **Start** بجانب **MySQL**
3. تأكد أن الحالة أصبحت **Running** (أخضر)

### 2. إعداد قاعدة البيانات
```bash
# شغل هذا الملف
setup_xampp_direct.bat
```

### 3. تشغيل الخدمات
```bash
# شغل هذا الملف
start_services_direct.bat
```

## 🎯 النتيجة المتوقعة

بعد التشغيل ستحصل على:
- **4 نوافذ Command Prompt** (واحدة لكل خدمة)
- **4 خدمات تعمل** على المنافذ 8001-8004

## 🧪 اختبار النظام

### اختبار سريع
افتح المتصفح واذهب إلى:
- http://localhost:8001/health
- http://localhost:8002/health  
- http://localhost:8003/health
- http://localhost:8004/health

يجب أن ترى: `{"status":"healthy","service":"service-name"}`

### اختبار شامل
```bash
# في مجلد المشروع
C:\xampp\php\php.exe tests/api_test_xampp.php
```

## 🔧 حل المشاكل الشائعة

### مشكلة: "PHP not found"
**الحل:**
1. تأكد من تثبيت XAMPP
2. تحقق من وجود الملف: `C:\xampp\php\php.exe`
3. أو أضف PHP إلى PATH

### مشكلة: "MySQL not running"
**الحل:**
1. افتح XAMPP Control Panel
2. اضغط Start بجانب MySQL
3. انتظر حتى يصبح أخضر

### مشكلة: "Port already in use"
**الحل:**
1. أغلق أي برامج تستخدم المنافذ 8001-8004
2. أعد تشغيل `start_services_direct.bat`

### مشكلة: "Database connection error"
**الحل:**
1. تأكد من تشغيل MySQL في XAMPP
2. أعد تشغيل `setup_xampp_direct.bat`
3. تحقق من كلمة مرور MySQL (افتراضياً فارغة)

## 📱 اختبار APIs

### تسجيل مستخدم جديد
```bash
curl -X POST http://localhost:8001/ ^
  -H "Content-Type: application/json" ^
  -d "{\"action\":\"register\",\"name\":\"Test User\",\"email\":\"<EMAIL>\",\"password\":\"password\"}"
```

### تسجيل دخول
```bash
curl -X POST http://localhost:8001/ ^
  -H "Content-Type: application/json" ^
  -d "{\"action\":\"login\",\"email\":\"<EMAIL>\",\"password\":\"password\"}"
```

### عرض المنتجات
```bash
curl http://localhost:8003/
```

## 🎉 الخطوات التالية

1. **جرب APIs** باستخدام Postman أو curl
2. **راجع قاعدة البيانات** في phpMyAdmin
3. **طور ميزات جديدة** حسب احتياجاتك
4. **فكر في تثبيت Docker** للمستقبل

## 💡 نصائح مهمة

- **احفظ tokens** من تسجيل الدخول للاستخدام في APIs المحمية
- **استخدم Postman** لاختبار APIs بسهولة
- **راقب نوافذ Command Prompt** للأخطاء
- **لا تغلق نوافذ الخدمات** إلا إذا كنت تريد إيقافها

## 🆘 الحصول على المساعدة

إذا واجهت مشاكل:
1. تحقق من **رسائل الخطأ** في نوافذ Command Prompt
2. راجع **MySQL logs** في XAMPP
3. تأكد من **إصدار PHP** (يفضل 7.4+)
4. جرب **إعادة تشغيل XAMPP** بالكامل
