<?php
require_once 'config/Database.php';
require_once 'models/Order.php';
require_once 'services/ProductService.php';
require_once 'services/NotificationService.php';

class OrderController {
    private $db;
    private $order;
    private $productService;
    private $notificationService;

    public function __construct() {
        $database = new Database();
        $this->db = $database->connect();
        $this->order = new Order($this->db);
        $this->productService = new ProductService();
        $this->notificationService = new NotificationService();
    }

    public function createOrder($userId, $data) {
        try {
            // Validate required fields
            if (!isset($data['items']) || empty($data['items'])) {
                return json_encode(['status' => 'error', 'message' => 'Order items required']);
            }

            // Validate and calculate total
            $totalAmount = 0;
            $orderItems = [];

            foreach ($data['items'] as $item) {
                if (!isset($item['product_id']) || !isset($item['quantity'])) {
                    return json_encode(['status' => 'error', 'message' => 'Invalid item format']);
                }

                // Get product details from product service
                $product = $this->productService->getProduct($item['product_id']);
                if (!$product) {
                    return json_encode(['status' => 'error', 'message' => 'Product not found: ' . $item['product_id']]);
                }

                // Check stock availability
                if ($product['stock'] < $item['quantity']) {
                    return json_encode(['status' => 'error', 'message' => 'Insufficient stock for product: ' . $product['name']]);
                }

                $itemTotal = $product['price'] * $item['quantity'];
                $totalAmount += $itemTotal;

                $orderItems[] = [
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'price' => $product['price'],
                    'total' => $itemTotal
                ];
            }

            // Create order
            $orderData = [
                'user_id' => $userId,
                'total_amount' => $totalAmount,
                'status' => 'pending',
                'shipping_address' => $data['shipping_address'] ?? null,
                'payment_method' => $data['payment_method'] ?? 'cash'
            ];

            $orderId = $this->order->create($orderData, $orderItems);

            if ($orderId) {
                // Update product stock
                foreach ($orderItems as $item) {
                    $this->productService->updateStock($item['product_id'], -$item['quantity']);
                }

                // Send notification
                $this->notificationService->sendOrderConfirmation($userId, $orderId);

                return json_encode([
                    'status' => 'success',
                    'message' => 'Order created successfully',
                    'order_id' => $orderId,
                    'total_amount' => $totalAmount
                ]);
            }

            return json_encode(['status' => 'error', 'message' => 'Failed to create order']);

        } catch (Exception $e) {
            return json_encode(['status' => 'error', 'message' => 'Order creation failed: ' . $e->getMessage()]);
        }
    }

    public function getOrder($orderId, $userId) {
        $order = $this->order->getById($orderId, $userId);
        if ($order) {
            return json_encode(['status' => 'success', 'data' => $order]);
        }
        return json_encode(['status' => 'error', 'message' => 'Order not found']);
    }

    public function listOrders($userId, $page = 1, $limit = 10, $status = null) {
        $orders = $this->order->getByUser($userId, $page, $limit, $status);
        return json_encode(['status' => 'success', 'data' => $orders]);
    }

    public function updateOrder($orderId, $userId, $data) {
        // Only allow status updates for now
        if (isset($data['status'])) {
            $allowedStatuses = ['pending', 'confirmed', 'shipped', 'delivered', 'cancelled'];
            if (!in_array($data['status'], $allowedStatuses)) {
                return json_encode(['status' => 'error', 'message' => 'Invalid status']);
            }

            if ($this->order->updateStatus($orderId, $userId, $data['status'])) {
                // Send notification for status change
                $this->notificationService->sendOrderStatusUpdate($userId, $orderId, $data['status']);
                
                return json_encode(['status' => 'success', 'message' => 'Order updated successfully']);
            }
        }

        return json_encode(['status' => 'error', 'message' => 'Failed to update order']);
    }

    public function cancelOrder($orderId, $userId) {
        $order = $this->order->getById($orderId, $userId);
        
        if (!$order) {
            return json_encode(['status' => 'error', 'message' => 'Order not found']);
        }

        if ($order['status'] === 'cancelled') {
            return json_encode(['status' => 'error', 'message' => 'Order already cancelled']);
        }

        if (in_array($order['status'], ['shipped', 'delivered'])) {
            return json_encode(['status' => 'error', 'message' => 'Cannot cancel shipped or delivered order']);
        }

        if ($this->order->updateStatus($orderId, $userId, 'cancelled')) {
            // Restore product stock
            $orderItems = $this->order->getOrderItems($orderId);
            foreach ($orderItems as $item) {
                $this->productService->updateStock($item['product_id'], $item['quantity']);
            }

            // Send notification
            $this->notificationService->sendOrderCancellation($userId, $orderId);

            return json_encode(['status' => 'success', 'message' => 'Order cancelled successfully']);
        }

        return json_encode(['status' => 'error', 'message' => 'Failed to cancel order']);
    }
}
