<?php
header('Content-Type: application/json');
require_once 'config/Database.php';
require_once 'models/User.php';
require_once 'utils/JWTHelper.php';

class AuthController {
    private $db;
    private $user;
    private $jwtHelper;

    public function __construct() {
        $database = new Database();
        $this->db = $database->connect();
        $this->user = new User($this->db);
        $this->jwtHelper = new JWTHelper();
    }

    public function login($email, $password) {
        $result = $this->user->login($email, $password);
        if ($result) {
            // Create JWT token
            $payload = [
                'user_id' => $result['id'],
                'email' => $email,
                'iat' => time(),
                'exp' => time() + (24 * 60 * 60) // 24 hours
            ];

            $token = $this->jwtHelper->encode($payload);
            $refreshToken = $this->jwtHelper->encode([
                'user_id' => $result['id'],
                'type' => 'refresh',
                'iat' => time(),
                'exp' => time() + (7 * 24 * 60 * 60) // 7 days
            ]);

            // Store refresh token in database
            $this->user->storeRefreshToken($result['id'], $refreshToken);

            return json_encode([
                'status' => 'success',
                'token' => $token,
                'refresh_token' => $refreshToken,
                'user_id' => $result['id'],
                'expires_in' => 24 * 60 * 60
            ]);
        }
        return json_encode(['status' => 'error', 'message' => 'Invalid credentials']);
    }

    public function register($name, $email, $password) {
        if ($this->user->emailExists($email)) {
            return json_encode(['status' => 'error', 'message' => 'Email already exists']);
        }

        $result = $this->user->create($name, $email, password_hash($password, PASSWORD_DEFAULT));
        if ($result) {
            return json_encode(['status' => 'success', 'message' => 'User registered successfully']);
        }
        return json_encode(['status' => 'error', 'message' => 'Registration failed']);
    }

    public function refreshToken($refreshToken) {
        try {
            $decoded = $this->jwtHelper->decode($refreshToken);

            if ($decoded['type'] !== 'refresh') {
                return json_encode(['status' => 'error', 'message' => 'Invalid refresh token']);
            }

            // Verify refresh token exists in database
            if (!$this->user->verifyRefreshToken($decoded['user_id'], $refreshToken)) {
                return json_encode(['status' => 'error', 'message' => 'Refresh token not found']);
            }

            // Create new access token
            $payload = [
                'user_id' => $decoded['user_id'],
                'iat' => time(),
                'exp' => time() + (24 * 60 * 60) // 24 hours
            ];

            $newToken = $this->jwtHelper->encode($payload);

            return json_encode([
                'status' => 'success',
                'token' => $newToken,
                'expires_in' => 24 * 60 * 60
            ]);

        } catch (Exception $e) {
            return json_encode(['status' => 'error', 'message' => 'Invalid refresh token']);
        }
    }

    public function logout($userId) {
        if ($this->user->clearRefreshToken($userId)) {
            return json_encode(['status' => 'success', 'message' => 'Logged out successfully']);
        }
        return json_encode(['status' => 'error', 'message' => 'Logout failed']);
    }

    public function verifyToken($token) {
        try {
            $decoded = $this->jwtHelper->decode($token);
            return json_encode([
                'status' => 'success',
                'valid' => true,
                'user_id' => $decoded['user_id']
            ]);
        } catch (Exception $e) {
            return json_encode([
                'status' => 'error',
                'valid' => false,
                'message' => 'Invalid token'
            ]);
        }
    }
}
