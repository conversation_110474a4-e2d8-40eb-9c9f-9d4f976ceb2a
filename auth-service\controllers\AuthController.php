<?php
header('Content-Type: application/json');
require_once 'config/Database.php';
require_once 'models/User.php';

class AuthController {
    private $db;
    private $user;

    public function __construct() {
        $database = new Database();
        $this->db = $database->connect();
        $this->user = new User($this->db);
    }

    public function login($email, $password) {
        $result = $this->user->login($email, $password);
        if ($result) {
            $token = bin2hex(random_bytes(32));
            // Store token in database
            $this->user->storeToken($result['id'], $token);
            return json_encode([
                'status' => 'success',
                'token' => $token,
                'user_id' => $result['id']
            ]);
        }
        return json_encode(['status' => 'error', 'message' => 'Invalid credentials']);
    }

    public function register($name, $email, $password) {
        if ($this->user->emailExists($email)) {
            return json_encode(['status' => 'error', 'message' => 'Email already exists']);
        }
        
        $result = $this->user->create($name, $email, password_hash($password, PASSWORD_DEFAULT));
        if ($result) {
            return json_encode(['status' => 'success', 'message' => 'User registered successfully']);
        }
        return json_encode(['status' => 'error', 'message' => 'Registration failed']);
    }
}
