@echo off
title Microservices Auto-Installer
color 0B
cls

echo.
echo  ███╗   ███╗██╗ ██████╗██████╗  ██████╗ ███████╗███████╗██████╗ ██╗   ██╗██╗ ██████╗███████╗███████╗
echo  ████╗ ████║██║██╔════╝██╔══██╗██╔═══██╗██╔════╝██╔════╝██╔══██╗██║   ██║██║██╔════╝██╔════╝██╔════╝
echo  ██╔████╔██║██║██║     ██████╔╝██║   ██║███████╗█████╗  ██████╔╝██║   ██║██║██║     █████╗  ███████╗
echo  ██║╚██╔╝██║██║██║     ██╔══██╗██║   ██║╚════██║██╔══╝  ██╔══██╗╚██╗ ██╔╝██║██║     ██╔══╝  ╚════██║
echo  ██║ ╚═╝ ██║██║╚██████╗██║  ██║╚██████╔╝███████║███████╗██║  ██║ ╚████╔╝ ██║╚██████╗███████╗███████║
echo  ╚═╝     ╚═╝╚═╝ ╚═════╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚═╝ ╚═════╝╚══════╝╚══════╝
echo.
echo                                    🚀 AUTO-INSTALLER v1.0
echo                                   PHP + MySQL Microservices
echo.
echo ================================================================================
echo.

:: Check if already installed
if exist ".installed" (
    echo ✅ System already installed!
    echo.
    echo 🚀 Starting services...
    call start_services_direct.bat
    exit /b 0
)

echo 🔍 STEP 1: Checking system requirements...
echo ----------------------------------------
echo.

:: Check for XAMPP installation
set XAMPP_FOUND=0
set PHP_PATH=""
set MYSQL_PATH=""

echo 🔍 Looking for XAMPP installation...

:: Check common XAMPP locations
if exist "C:\xampp\php\php.exe" (
    set PHP_PATH="C:\xampp\php\php.exe"
    set MYSQL_PATH="C:\xampp\mysql\bin\mysql.exe"
    set XAMPP_FOUND=1
    echo ✅ Found XAMPP at: C:\xampp\
)

if exist "C:\XAMPP\php\php.exe" (
    set PHP_PATH="C:\XAMPP\php\php.exe"
    set MYSQL_PATH="C:\XAMPP\mysql\bin\mysql.exe"
    set XAMPP_FOUND=1
    echo ✅ Found XAMPP at: C:\XAMPP\
)

if exist "%USERPROFILE%\xampp\php\php.exe" (
    set PHP_PATH="%USERPROFILE%\xampp\php\php.exe"
    set MYSQL_PATH="%USERPROFILE%\xampp\mysql\bin\mysql.exe"
    set XAMPP_FOUND=1
    echo ✅ Found XAMPP at: %USERPROFILE%\xampp\
)

:: If XAMPP not found, try to download and install
if %XAMPP_FOUND%==0 (
    echo ❌ XAMPP not found!
    echo.
    echo 🔽 Would you like to download and install XAMPP automatically?
    echo    This will download XAMPP from the official website.
    echo.
    set /p DOWNLOAD_XAMPP="Download XAMPP? (y/n): "
    
    if /i "%DOWNLOAD_XAMPP%"=="y" (
        call :download_xampp
    ) else (
        echo.
        echo 💡 Please install XAMPP manually:
        echo    1. Go to: https://www.apachefriends.org/download.html
        echo    2. Download XAMPP for Windows
        echo    3. Install it to C:\xampp\
        echo    4. Run this installer again
        echo.
        pause
        exit /b 1
    )
)

echo.
echo 🔍 STEP 2: Checking PHP version...
echo ----------------------------------
%PHP_PATH% --version
if %errorlevel% neq 0 (
    echo ❌ PHP check failed!
    pause
    exit /b 1
)
echo ✅ PHP is working correctly
echo.

echo 🔍 STEP 3: Checking MySQL service...
echo ------------------------------------
netstat -an | findstr :3306 >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ MySQL not running. Attempting to start...
    
    :: Try to start MySQL service
    if exist "C:\xampp\mysql_start.bat" (
        call "C:\xampp\mysql_start.bat"
    ) else if exist "C:\XAMPP\mysql_start.bat" (
        call "C:\XAMPP\mysql_start.bat"
    ) else (
        echo ❌ Could not start MySQL automatically.
        echo.
        echo 💡 Please start MySQL manually:
        echo    1. Open XAMPP Control Panel
        echo    2. Click 'Start' next to MySQL
        echo    3. Wait for it to turn green
        echo    4. Run this installer again
        echo.
        pause
        exit /b 1
    )
    
    :: Wait and check again
    timeout /t 5 /nobreak >nul
    netstat -an | findstr :3306 >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ MySQL still not running.
        echo 💡 Please start MySQL in XAMPP Control Panel and try again.
        pause
        exit /b 1
    )
)
echo ✅ MySQL is running on port 3306
echo.

echo 🗄️ STEP 4: Setting up database...
echo ----------------------------------
echo 📋 Creating database and tables...
%PHP_PATH% auto_setup.php
if %errorlevel% neq 0 (
    echo ❌ Database setup failed!
    pause
    exit /b 1
)
echo ✅ Database setup completed successfully
echo.

echo 📁 STEP 5: Creating configuration files...
echo -------------------------------------------
echo 📝 Generating service configuration...

:: Create service configuration
echo PHP_PATH=%PHP_PATH% > .config
echo MYSQL_PATH=%MYSQL_PATH% >> .config
echo INSTALL_DATE=%date% %time% >> .config

echo ✅ Configuration files created
echo.

echo 🧪 STEP 6: Testing installation...
echo ----------------------------------
echo 🔍 Testing database connection...
%PHP_PATH% test_installation.php
if %errorlevel% neq 0 (
    echo ❌ Installation test failed!
    pause
    exit /b 1
)
echo ✅ Installation test passed
echo.

echo 📋 STEP 7: Creating shortcuts...
echo --------------------------------
echo 🔗 Creating desktop shortcuts...
call :create_shortcuts
echo ✅ Shortcuts created
echo.

:: Mark as installed
echo INSTALLED=true > .installed
echo INSTALL_DATE=%date% %time% >> .installed
echo PHP_PATH=%PHP_PATH% >> .installed

echo.
echo 🎉 INSTALLATION COMPLETED SUCCESSFULLY!
echo ================================================================================
echo.
echo ✅ System is ready to use!
echo.
echo 📍 What's been installed:
echo    ✓ Database created with sample data
echo    ✓ All services configured
echo    ✓ Desktop shortcuts created
echo    ✓ Configuration files generated
echo.
echo 🚀 Starting services now...
echo.

:: Auto-start services
call start_services_direct.bat

echo.
echo 💡 Next time, just double-click "Start Microservices" on your desktop!
echo.
pause
exit /b 0

:: Function to download XAMPP
:download_xampp
echo.
echo 🔽 Downloading XAMPP...
echo This may take several minutes depending on your internet speed.
echo.

:: Create download directory
if not exist "downloads" mkdir downloads

:: Download XAMPP (using PowerShell)
powershell -Command "& {Invoke-WebRequest -Uri 'https://sourceforge.net/projects/xampp/files/XAMPP%%20Windows/8.2.4/xampp-windows-x64-8.2.4-0-VS16-installer.exe/download' -OutFile 'downloads\xampp-installer.exe'}"

if exist "downloads\xampp-installer.exe" (
    echo ✅ Download completed!
    echo.
    echo 🔧 Starting XAMPP installation...
    echo Please follow the installation wizard.
    echo.
    start /wait "downloads\xampp-installer.exe"
    
    :: Check if installation was successful
    if exist "C:\xampp\php\php.exe" (
        set PHP_PATH="C:\xampp\php\php.exe"
        set MYSQL_PATH="C:\xampp\mysql\bin\mysql.exe"
        set XAMPP_FOUND=1
        echo ✅ XAMPP installed successfully!
    ) else (
        echo ❌ XAMPP installation failed or installed to different location.
        pause
        exit /b 1
    )
) else (
    echo ❌ Download failed!
    echo 💡 Please download XAMPP manually from: https://www.apachefriends.org/
    pause
    exit /b 1
)
goto :eof

:: Function to create shortcuts
:create_shortcuts
:: Create Start Services shortcut
echo Set oWS = WScript.CreateObject("WScript.Shell") > create_shortcut.vbs
echo sLinkFile = "%USERPROFILE%\Desktop\Start Microservices.lnk" >> create_shortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> create_shortcut.vbs
echo oLink.TargetPath = "%CD%\start_services_direct.bat" >> create_shortcut.vbs
echo oLink.WorkingDirectory = "%CD%" >> create_shortcut.vbs
echo oLink.Description = "Start Microservices System" >> create_shortcut.vbs
echo oLink.IconLocation = "shell32.dll,25" >> create_shortcut.vbs
echo oLink.Save >> create_shortcut.vbs

:: Create Test APIs shortcut
echo Set oWS = WScript.CreateObject("WScript.Shell") > create_test_shortcut.vbs
echo sLinkFile = "%USERPROFILE%\Desktop\Test Microservices APIs.lnk" >> create_test_shortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> create_test_shortcut.vbs
echo oLink.TargetPath = "%PHP_PATH%" >> create_test_shortcut.vbs
echo oLink.Arguments = "tests\api_test_xampp.php" >> create_test_shortcut.vbs
echo oLink.WorkingDirectory = "%CD%" >> create_test_shortcut.vbs
echo oLink.Description = "Test Microservices APIs" >> create_test_shortcut.vbs
echo oLink.IconLocation = "shell32.dll,24" >> create_test_shortcut.vbs
echo oLink.Save >> create_test_shortcut.vbs

cscript create_shortcut.vbs >nul
cscript create_test_shortcut.vbs >nul
del create_shortcut.vbs
del create_test_shortcut.vbs
goto :eof
