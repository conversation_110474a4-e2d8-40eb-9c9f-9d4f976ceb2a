<!DOCTYPE html>
<html>
<head>
    <title>Microservices Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin: 10px 0; }
        input, button { padding: 10px; margin: 5px; }
        .result { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Microservices Login Test</h1>
        
        <h2>1. Health Check</h2>
        <button onclick="testHealth()">Test Auth Service Health</button>
        <div id="healthResult" class="result"></div>
        
        <h2>2. Login Test</h2>
        <div class="form-group">
            <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        </div>
        <div class="form-group">
            <input type="password" id="password" placeholder="Password" value="password">
        </div>
        <button onclick="testLogin()">Login</button>
        <div id="loginResult" class="result"></div>
        
        <h2>3. Register New User</h2>
        <div class="form-group">
            <input type="text" id="regName" placeholder="Name" value="Test User">
        </div>
        <div class="form-group">
            <input type="email" id="regEmail" placeholder="Email" value="<EMAIL>">
        </div>
        <div class="form-group">
            <input type="password" id="regPassword" placeholder="Password" value="password123">
        </div>
        <button onclick="testRegister()">Register</button>
        <div id="registerResult" class="result"></div>
        
        <h2>4. Test Products (No Auth Required)</h2>
        <button onclick="testProducts()">Get Products</button>
        <div id="productsResult" class="result"></div>
    </div>

    <script>
        async function testHealth() {
            const resultDiv = document.getElementById('healthResult');
            resultDiv.innerHTML = '⏳ Testing...';
            
            try {
                const response = await fetch('http://localhost:8001/health');
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    resultDiv.innerHTML = '✅ Auth Service is healthy!';
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = '⚠️ Unexpected response: ' + JSON.stringify(data);
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = '❌ Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
        
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '⏳ Logging in...';
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('http://localhost:8001/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    resultDiv.innerHTML = `
                        ✅ Login successful!<br>
                        🔑 Token: ${data.token.substring(0, 50)}...<br>
                        👤 User ID: ${data.user_id}<br>
                        ⏰ Expires in: ${data.expires_in} seconds
                    `;
                    resultDiv.className = 'result success';
                    
                    // Save token for other tests
                    window.authToken = data.token;
                } else {
                    resultDiv.innerHTML = '❌ Login failed: ' + data.message;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = '❌ Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
        
        async function testRegister() {
            const resultDiv = document.getElementById('registerResult');
            resultDiv.innerHTML = '⏳ Registering...';
            
            const name = document.getElementById('regName').value;
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            
            try {
                const response = await fetch('http://localhost:8001/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'register',
                        name: name,
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    resultDiv.innerHTML = '✅ Registration successful!';
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = '❌ Registration failed: ' + data.message;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = '❌ Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
        
        async function testProducts() {
            const resultDiv = document.getElementById('productsResult');
            resultDiv.innerHTML = '⏳ Loading products...';
            
            try {
                const response = await fetch('http://localhost:8003/');
                const data = await response.json();
                
                if (data.status === 'success') {
                    resultDiv.innerHTML = `
                        ✅ Products loaded successfully!<br>
                        📦 Found ${data.data.length} products<br>
                        <details>
                            <summary>View Products</summary>
                            <pre>${JSON.stringify(data.data, null, 2)}</pre>
                        </details>
                    `;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = '❌ Failed to load products: ' + data.message;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = '❌ Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
