<?php
class RateLimiter {
    private $redisHost;
    private $redisPort;
    private $redis;
    private $maxRequests;
    private $timeWindow;
    
    public function __construct($maxRequests = 100, $timeWindow = 3600) {
        $this->redisHost = getenv('REDIS_HOST') ?: 'redis';
        $this->redisPort = getenv('REDIS_PORT') ?: 6379;
        $this->maxRequests = $maxRequests;
        $this->timeWindow = $timeWindow; // 1 hour
        
        $this->initRedis();
    }
    
    private function initRedis() {
        try {
            if (class_exists('Redis')) {
                $this->redis = new Redis();
                $this->redis->connect($this->redisHost, $this->redisPort);
            }
        } catch (Exception $e) {
            // Fallback to file-based rate limiting if Redis is not available
            $this->redis = null;
        }
    }
    
    public function isAllowed($clientIp) {
        if ($this->redis) {
            return $this->redisRateLimit($clientIp);
        } else {
            return $this->fileRateLimit($clientIp);
        }
    }
    
    private function redisRateLimit($clientIp) {
        $key = "rate_limit:$clientIp";
        $current = $this->redis->get($key);
        
        if ($current === false) {
            // First request
            $this->redis->setex($key, $this->timeWindow, 1);
            return true;
        }
        
        if ($current >= $this->maxRequests) {
            return false;
        }
        
        $this->redis->incr($key);
        return true;
    }
    
    private function fileRateLimit($clientIp) {
        $rateLimitDir = '/tmp/rate_limits';
        if (!is_dir($rateLimitDir)) {
            mkdir($rateLimitDir, 0777, true);
        }
        
        $filename = $rateLimitDir . '/' . md5($clientIp) . '.txt';
        $now = time();
        
        if (!file_exists($filename)) {
            file_put_contents($filename, json_encode(['count' => 1, 'reset_time' => $now + $this->timeWindow]));
            return true;
        }
        
        $data = json_decode(file_get_contents($filename), true);
        
        if ($now > $data['reset_time']) {
            // Reset counter
            file_put_contents($filename, json_encode(['count' => 1, 'reset_time' => $now + $this->timeWindow]));
            return true;
        }
        
        if ($data['count'] >= $this->maxRequests) {
            return false;
        }
        
        $data['count']++;
        file_put_contents($filename, json_encode($data));
        return true;
    }
    
    public function getRemainingRequests($clientIp) {
        if ($this->redis) {
            $key = "rate_limit:$clientIp";
            $current = $this->redis->get($key);
            return $current === false ? $this->maxRequests : max(0, $this->maxRequests - $current);
        }
        
        return $this->maxRequests; // Simplified for file-based
    }
}
