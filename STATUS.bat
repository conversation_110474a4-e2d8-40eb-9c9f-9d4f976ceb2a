@echo off
title Microservices - System Status
color 0A

echo.
echo 📊 Microservices System Status
echo ==============================
echo.

:: Check system capabilities
echo 🔍 System Capabilities:
echo -----------------------

:: Check Docker
docker --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Docker: Available
    docker-compose --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Docker Compose: Available
    ) else (
        echo ❌ Docker Compose: Not Available
    )
) else (
    echo ❌ Docker: Not Available
)

:: Check XAMPP
if exist "C:\xampp\php\php.exe" (
    echo ✅ XAMPP: Available at C:\xampp\
) else if exist "C:\XAMPP\php\php.exe" (
    echo ✅ XAMPP: Available at C:\XAMPP\
) else if exist "%USERPROFILE%\xampp\php\php.exe" (
    echo ✅ XAMPP: Available at %USERPROFILE%\xampp\
) else (
    echo ❌ XAMPP: Not Available
)

echo.
echo 🚀 Current Status:
echo ------------------

:: Check if services are running
set SERVICES_RUNNING=0

:: Check Docker services
if exist ".running_docker" (
    echo 🐳 Docker Services: RUNNING
    set SERVICES_RUNNING=1
    
    echo   📍 Checking Docker containers...
    docker-compose ps 2>nul | findstr "Up" >nul
    if %errorlevel% equ 0 (
        echo   ✅ Containers are active
    ) else (
        echo   ⚠️ Containers may be starting or stopped
    )
)

:: Check XAMPP services
if exist ".running_xampp" (
    echo 🔧 XAMPP Services: RUNNING
    set SERVICES_RUNNING=1
    
    echo   📍 Checking PHP processes...
    tasklist /fi "imagename eq php.exe" 2>nul | findstr "php.exe" >nul
    if %errorlevel% equ 0 (
        echo   ✅ PHP services are active
    ) else (
        echo   ⚠️ PHP services may be stopped
    )
    
    echo   📍 Checking MySQL...
    netstat -an | findstr :3306 >nul 2>&1
    if %errorlevel% equ 0 (
        echo   ✅ MySQL is running
    ) else (
        echo   ❌ MySQL is not running
    )
)

if %SERVICES_RUNNING%==0 (
    echo ⭕ No services currently running
)

echo.
echo 🧪 Service Health Check:
echo ------------------------

if %SERVICES_RUNNING%==1 (
    echo Testing service endpoints...
    
    :: Test each service
    curl -s http://localhost:8001/health >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Auth Service (8001): Healthy
    ) else (
        echo ❌ Auth Service (8001): Not responding
    )
    
    curl -s http://localhost:8002/health >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ User Service (8002): Healthy
    ) else (
        echo ❌ User Service (8002): Not responding
    )
    
    curl -s http://localhost:8003/health >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Product Service (8003): Healthy
    ) else (
        echo ❌ Product Service (8003): Not responding
    )
    
    curl -s http://localhost:8004/health >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Order Service (8004): Healthy
    ) else (
        echo ❌ Order Service (8004): Not responding
    )
    
    :: Check API Gateway if using Docker
    if exist ".running_docker" (
        curl -s http://localhost:8000/health >nul 2>&1
        if %errorlevel% equ 0 (
            echo ✅ API Gateway (8000): Healthy
        ) else (
            echo ❌ API Gateway (8000): Not responding
        )
    )
) else (
    echo ℹ️ No services to test (not running)
)

echo.
echo 📁 System Files:
echo ----------------

if exist ".installed" (
    echo ✅ System: Installed
) else (
    echo ❌ System: Not installed
)

if exist "docker-compose.yml" (
    echo ✅ Docker Config: Available
) else (
    echo ❌ Docker Config: Missing
)

if exist ".env" (
    echo ✅ Environment Config: Available
) else (
    echo ⚠️ Environment Config: Missing
)

if exist "config.json" (
    echo ✅ System Config: Available
) else (
    echo ⚠️ System Config: Missing
)

echo.
echo 💡 Quick Actions:
echo -----------------
echo   🚀 Start System: RUN.bat
echo   🛑 Stop System: STOP.bat
echo   🔄 Update System: update_system.bat
echo   🧪 Test APIs: tests\api_test_xampp.php
echo.

if %SERVICES_RUNNING%==1 (
    echo 📍 Service URLs:
    echo   🔐 Auth: http://localhost:8001
    echo   👤 User: http://localhost:8002
    echo   🛍️ Product: http://localhost:8003
    echo   📦 Order: http://localhost:8004
    
    if exist ".running_docker" (
        echo   🌐 Gateway: http://localhost:8000
    )
    echo.
)

pause
