@echo off
title Fix Docker WSL2 Issue
color 0E

echo.
echo 🔧 Docker WSL2 Fix Tool
echo =======================
echo.

echo ❌ Docker WSL2 Issue Detected!
echo.
echo 💡 The issue is: WSL2 is not supported with your current machine configuration
echo.
echo 🛠️ Solutions available:
echo   [1] Enable WSL2 and Virtual Machine Platform (Recommended)
echo   [2] Use XAMPP instead (Quick alternative)
echo   [3] Use Docker Toolbox (Legacy Docker)
echo.

set /p CHOICE="Choose your solution (1, 2, or 3): "

if "%CHOICE%"=="1" goto enable_wsl2
if "%CHOICE%"=="2" goto use_xampp
if "%CHOICE%"=="3" goto docker_toolbox
goto invalid_choice

:enable_wsl2
echo.
echo 🔧 Enabling WSL2 and Virtual Machine Platform...
echo.
echo ⚠️ This requires administrator privileges and a restart!
echo.
set /p CONFIRM="Continue? (y/n): "
if /i not "%CONFIRM%"=="y" goto use_xampp

echo 📋 Step 1: Enabling Windows features...
echo This will open Windows Features dialog...
echo ✅ Please check:
echo   - Virtual Machine Platform
echo   - Windows Subsystem for Linux
echo.
pause
start optionalfeatures.exe

echo.
echo 📋 Step 2: Installing WSL2...
echo Running: wsl --install --no-distribution
wsl --install --no-distribution

echo.
echo 📋 Step 3: Setting WSL2 as default...
wsl --set-default-version 2

echo.
echo ✅ WSL2 setup completed!
echo.
echo ⚠️ IMPORTANT: You MUST restart your computer now!
echo After restart, run RUN.bat again to use Docker.
echo.
echo 🔄 Restart now?
set /p RESTART="Restart computer? (y/n): "
if /i "%RESTART%"=="y" shutdown /r /t 10 /c "Restarting for WSL2 setup"
goto end

:use_xampp
echo.
echo 🔧 Switching to XAMPP mode...
echo.
echo ✅ XAMPP is simpler and doesn't need WSL2
echo 📋 The system will automatically use XAMPP instead of Docker
echo.

:: Create preference file for XAMPP
echo PREFER_XAMPP=true > .docker_disabled
echo REASON=WSL2_NOT_SUPPORTED >> .docker_disabled
echo DATE=%date% %time% >> .docker_disabled

echo ✅ System configured to use XAMPP
echo.
echo 🚀 Starting with XAMPP...
call start_services_direct.bat
goto end

:docker_toolbox
echo.
echo 📥 Docker Toolbox (Legacy) Option
echo.
echo ⚠️ Docker Toolbox is deprecated but works without WSL2
echo.
echo 🔽 Download Docker Toolbox from:
echo https://github.com/docker/toolbox/releases
echo.
echo 💡 Or switch to XAMPP for easier setup
echo.
set /p TOOLBOX_CHOICE="Download Docker Toolbox (d) or use XAMPP (x)? "
if /i "%TOOLBOX_CHOICE%"=="d" (
    start https://github.com/docker/toolbox/releases
    echo 📋 After installing Docker Toolbox, run RUN.bat again
) else (
    goto use_xampp
)
goto end

:invalid_choice
echo.
echo ❌ Invalid choice. Using XAMPP as default...
goto use_xampp

:end
echo.
echo 💡 Summary:
echo   - If you enabled WSL2: Restart and run RUN.bat
echo   - If you chose XAMPP: System is starting now
echo   - If you chose Docker Toolbox: Install it first
echo.
pause
