# Microservices Management Makefile

.PHONY: help build up down restart logs clean test health

# Default target
help:
	@echo "Available commands:"
	@echo "  build     - Build all Docker images"
	@echo "  up        - Start all services"
	@echo "  down      - Stop all services"
	@echo "  restart   - Restart all services"
	@echo "  logs      - Show logs for all services"
	@echo "  clean     - Clean up containers and volumes"
	@echo "  test      - Run API tests"
	@echo "  health    - Check health of all services"
	@echo "  db-reset  - Reset database with fresh data"

# Build all images
build:
	docker-compose build

# Start all services
up:
	docker-compose up -d
	@echo "Services are starting..."
	@echo "API Gateway: http://localhost:8000"
	@echo "Auth Service: http://localhost:8001"
	@echo "User Service: http://localhost:8002"
	@echo "Product Service: http://localhost:8003"
	@echo "Order Service: http://localhost:8004"

# Stop all services
down:
	docker-compose down

# Restart all services
restart:
	docker-compose restart

# Show logs
logs:
	docker-compose logs -f

# Show logs for specific service
logs-auth:
	docker-compose logs -f auth-service

logs-user:
	docker-compose logs -f user-service

logs-product:
	docker-compose logs -f product-service

logs-order:
	docker-compose logs -f order-service

logs-gateway:
	docker-compose logs -f api-gateway

logs-db:
	docker-compose logs -f db

# Clean up
clean:
	docker-compose down -v
	docker system prune -f

# Reset database
db-reset:
	docker-compose down db
	docker volume rm haitham_db_data
	docker-compose up -d db
	@echo "Database reset complete"

# Health check all services
health:
	@echo "Checking service health..."
	@curl -s http://localhost:8000/health || echo "API Gateway: DOWN"
	@curl -s http://localhost:8001/health || echo "Auth Service: DOWN"
	@curl -s http://localhost:8002/health || echo "User Service: DOWN"
	@curl -s http://localhost:8003/health || echo "Product Service: DOWN"
	@curl -s http://localhost:8004/health || echo "Order Service: DOWN"

# API Tests
test:
	@echo "Running API tests..."
	@echo "Testing Auth Service..."
	@curl -X POST http://localhost:8000/auth \
		-H "Content-Type: application/json" \
		-d '{"action":"register","name":"Test User","email":"<EMAIL>","password":"password"}' \
		|| echo "Auth test failed"
	
	@echo "Testing Product Service..."
	@curl -s http://localhost:8000/products || echo "Product test failed"

# Development helpers
dev-setup:
	cp .env.example .env
	mkdir -p logs/{api-gateway,auth-service,user-service,product-service,order-service,mysql}
	@echo "Development environment setup complete"

# Production deployment
prod-deploy:
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Backup database
backup:
	docker exec haitham_db_1 mysqldump -u root -proot microservices_db > backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "Database backup created"

# Restore database
restore:
	@read -p "Enter backup file name: " file; \
	docker exec -i haitham_db_1 mysql -u root -proot microservices_db < $$file

# Monitor services
monitor:
	watch -n 2 'docker-compose ps'

# Scale services
scale-auth:
	docker-compose up -d --scale auth-service=2

scale-user:
	docker-compose up -d --scale user-service=2

scale-product:
	docker-compose up -d --scale product-service=2

scale-order:
	docker-compose up -d --scale order-service=2
