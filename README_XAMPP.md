# 🚀 تشغيل نظام المايكروسيرفس مع XAMPP

دليل سريع لتشغيل النظام باستخدام XAMPP بدلاً من Docker.

## 📋 المتطلبات

- ✅ XAMPP مثبت ويعمل
- ✅ PHP 7.4+ 
- ✅ MySQL يعمل في XAMPP

## 🏃‍♂️ خطوات التشغيل السريع

### 1. تأكد من تشغيل XAMPP
```bash
# تحقق من أن MySQL يعمل
netstat -an | findstr :3306
```

### 2. إعداد قاعدة البيانات
```bash
php setup_xampp.php
```

### 3. تشغيل الخدمات
```bash
xampp_setup.bat
```

## 🌐 الخدمات والمنافذ

بعد التشغيل، ستكون الخدمات متاحة على:

- **Auth Service**: http://localhost:8001
- **User Service**: http://localhost:8002  
- **Product Service**: http://localhost:8003
- **Order Service**: http://localhost:8004

## 🧪 اختبار النظام

### اختبار صحة الخدمات
```bash
curl http://localhost:8001/health
curl http://localhost:8002/health
curl http://localhost:8003/health
curl http://localhost:8004/health
```

### اختبار تسجيل مستخدم جديد
```bash
curl -X POST http://localhost:8001/ ^
  -H "Content-Type: application/json" ^
  -d "{\"action\":\"register\",\"name\":\"Test User\",\"email\":\"<EMAIL>\",\"password\":\"password\"}"
```

### اختبار تسجيل الدخول
```bash
curl -X POST http://localhost:8001/ ^
  -H "Content-Type: application/json" ^
  -d "{\"action\":\"login\",\"email\":\"<EMAIL>\",\"password\":\"password\"}"
```

### اختبار عرض المنتجات
```bash
curl http://localhost:8003/
```

## 🔧 استكشاف الأخطاء

### مشكلة: "Connection refused"
**الحل:**
1. تأكد من تشغيل XAMPP MySQL
2. تحقق من المنفذ 3306:
   ```bash
   netstat -an | findstr :3306
   ```

### مشكلة: "Database connection error"
**الحل:**
1. افتح XAMPP Control Panel
2. تأكد من أن MySQL يعمل (أخضر)
3. أعد تشغيل `php setup_xampp.php`

### مشكلة: "Port already in use"
**الحل:**
1. أغلق أي خدمات تعمل على المنافذ 8001-8004
2. أعد تشغيل `xampp_setup.bat`

## 📱 اختبار شامل

شغل ملف الاختبار:
```bash
php tests/api_test_xampp.php
```

## 🔄 إعادة التشغيل

لإعادة تشغيل الخدمات:
1. أغلق نوافذ الـ Command Prompt للخدمات
2. شغل `xampp_setup.bat` مرة أخرى

## 📊 مراقبة الخدمات

يمكنك مراقبة الخدمات من خلال:
- نوافذ Command Prompt المفتوحة
- XAMPP Control Panel للـ MySQL
- Task Manager للعمليات

## 🎯 الخطوات التالية

بعد التأكد من عمل النظام:

1. **جرب API endpoints** المختلفة
2. **اختبر إنشاء طلبات** جديدة
3. **راجع قاعدة البيانات** في phpMyAdmin
4. **طور ميزات جديدة** حسب احتياجاتك

## 💡 نصائح

- استخدم **Postman** لاختبار APIs بسهولة
- راجع **logs** في نوافذ Command Prompt
- استخدم **phpMyAdmin** لإدارة قاعدة البيانات
- احفظ **tokens** للاستخدام في الطلبات المحمية

## 🆘 الحصول على المساعدة

إذا واجهت مشاكل:
1. تحقق من **error messages** في Command Prompt
2. راجع **MySQL logs** في XAMPP
3. تأكد من **PHP version** (يجب أن يكون 7.4+)
4. تحقق من **file permissions**
