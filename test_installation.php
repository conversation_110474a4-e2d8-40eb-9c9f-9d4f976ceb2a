<?php
/**
 * Installation Test Script
 * Verifies that the system is properly installed and configured
 */

echo "🧪 Testing Microservices Installation\n";
echo "====================================\n\n";

$errors = 0;
$warnings = 0;

// Test 1: Database Connection
echo "🔍 Test 1: Database Connection\n";
echo "------------------------------\n";
try {
    $pdo = new PDO('mysql:host=localhost;dbname=microservices_db', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connection: OK\n";
} catch (PDOException $e) {
    echo "❌ Database connection: FAILED - " . $e->getMessage() . "\n";
    $errors++;
}
echo "\n";

// Test 2: Required Tables
echo "🔍 Test 2: Database Tables\n";
echo "--------------------------\n";
$requiredTables = ['users', 'products', 'orders', 'order_items', 'notifications'];
foreach ($requiredTables as $table) {
    try {
        $stmt = $pdo->query("DESCRIBE $table");
        echo "✅ Table '$table': EXISTS\n";
    } catch (PDOException $e) {
        echo "❌ Table '$table': MISSING\n";
        $errors++;
    }
}
echo "\n";

// Test 3: Sample Data
echo "🔍 Test 3: Sample Data\n";
echo "----------------------\n";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $users = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
    $products = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($users['count'] > 0) {
        echo "✅ Users table: {$users['count']} records\n";
    } else {
        echo "⚠️ Users table: No records (warning)\n";
        $warnings++;
    }
    
    if ($products['count'] > 0) {
        echo "✅ Products table: {$products['count']} records\n";
    } else {
        echo "⚠️ Products table: No records (warning)\n";
        $warnings++;
    }
} catch (PDOException $e) {
    echo "❌ Sample data check: FAILED - " . $e->getMessage() . "\n";
    $errors++;
}
echo "\n";

// Test 4: Configuration Files
echo "🔍 Test 4: Configuration Files\n";
echo "-------------------------------\n";
$configFiles = ['.env', 'config.json', 'api_docs.json'];
foreach ($configFiles as $file) {
    if (file_exists($file)) {
        echo "✅ Config file '$file': EXISTS\n";
    } else {
        echo "⚠️ Config file '$file': MISSING (warning)\n";
        $warnings++;
    }
}
echo "\n";

// Test 5: Service Files
echo "🔍 Test 5: Service Files\n";
echo "------------------------\n";
$services = [
    'auth-service/index.php',
    'user-service/index.php', 
    'product-service/index.php',
    'order-service/index.php'
];

foreach ($services as $service) {
    if (file_exists($service)) {
        echo "✅ Service '$service': EXISTS\n";
    } else {
        echo "❌ Service '$service': MISSING\n";
        $errors++;
    }
}
echo "\n";

// Test 6: Required Directories
echo "🔍 Test 6: Directory Structure\n";
echo "------------------------------\n";
$directories = [
    'auth-service',
    'user-service',
    'product-service', 
    'order-service',
    'shared/middleware',
    'tests'
];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        echo "✅ Directory '$dir': EXISTS\n";
    } else {
        echo "❌ Directory '$dir': MISSING\n";
        $errors++;
    }
}
echo "\n";

// Test 7: PHP Extensions
echo "🔍 Test 7: PHP Extensions\n";
echo "-------------------------\n";
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'curl'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ PHP extension '$ext': LOADED\n";
    } else {
        echo "❌ PHP extension '$ext': MISSING\n";
        $errors++;
    }
}
echo "\n";

// Test 8: JWT Helper
echo "🔍 Test 8: JWT Helper\n";
echo "---------------------\n";
if (file_exists('auth-service/utils/JWTHelper.php')) {
    require_once 'auth-service/utils/JWTHelper.php';
    try {
        $jwt = new JWTHelper();
        $token = $jwt->encode(['test' => 'data', 'exp' => time() + 3600]);
        $decoded = $jwt->decode($token);
        
        if ($decoded['test'] === 'data') {
            echo "✅ JWT Helper: WORKING\n";
        } else {
            echo "❌ JWT Helper: DECODE FAILED\n";
            $errors++;
        }
    } catch (Exception $e) {
        echo "❌ JWT Helper: ERROR - " . $e->getMessage() . "\n";
        $errors++;
    }
} else {
    echo "❌ JWT Helper: FILE MISSING\n";
    $errors++;
}
echo "\n";

// Test 9: Database Operations
echo "🔍 Test 9: Database Operations\n";
echo "------------------------------\n";
try {
    // Test INSERT
    $stmt = $pdo->prepare("INSERT INTO users (name, email, password) VALUES (?, ?, ?)");
    $testEmail = 'test_install_' . time() . '@example.com';
    $stmt->execute(['Test Install', $testEmail, 'test_hash']);
    $testUserId = $pdo->lastInsertId();
    echo "✅ Database INSERT: OK\n";
    
    // Test SELECT
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$testUserId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($user && $user['email'] === $testEmail) {
        echo "✅ Database SELECT: OK\n";
    } else {
        echo "❌ Database SELECT: FAILED\n";
        $errors++;
    }
    
    // Test UPDATE
    $stmt = $pdo->prepare("UPDATE users SET name = ? WHERE id = ?");
    $stmt->execute(['Test Install Updated', $testUserId]);
    echo "✅ Database UPDATE: OK\n";
    
    // Test DELETE (cleanup)
    $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
    $stmt->execute([$testUserId]);
    echo "✅ Database DELETE: OK\n";
    
} catch (PDOException $e) {
    echo "❌ Database operations: FAILED - " . $e->getMessage() . "\n";
    $errors++;
}
echo "\n";

// Test Results
echo "📊 TEST RESULTS\n";
echo "===============\n";
echo "Total Tests: 9\n";
echo "Errors: $errors\n";
echo "Warnings: $warnings\n\n";

if ($errors === 0) {
    echo "🎉 ALL TESTS PASSED!\n";
    echo "✅ Installation is successful and ready to use.\n\n";
    
    echo "🚀 Next Steps:\n";
    echo "  1. Services will start automatically\n";
    echo "  2. Test APIs at: http://localhost:8001-8004\n";
    echo "  3. Admin login: <EMAIL> / admin123\n";
    echo "  4. Check API docs: api_docs.json\n\n";
    
    exit(0);
} else {
    echo "❌ INSTALLATION HAS ISSUES!\n";
    echo "Please fix the errors above before proceeding.\n\n";
    
    if ($errors > 0) {
        echo "💡 Common fixes:\n";
        echo "  - Ensure MySQL is running in XAMPP\n";
        echo "  - Check file permissions\n";
        echo "  - Verify PHP extensions are installed\n";
        echo "  - Re-run the installer\n\n";
    }
    
    exit(1);
}
?>
