@echo off
title Microservices - Smart Launcher
color 0B
cls

echo.
echo  ███╗   ███╗██╗ ██████╗██████╗  ██████╗ ███████╗███████╗██████╗ ██╗   ██╗██╗ ██████╗███████╗███████╗
echo  ████╗ ████║██║██╔════╝██╔══██╗██╔═══██╗██╔════╝██╔════╝██╔══██╗██║   ██║██║██╔════╝██╔════╝██╔════╝
echo  ██╔████╔██║██║██║     ██████╔╝██║   ██║███████╗█████╗  ██████╔╝██║   ██║██║██║     █████╗  ███████╗
echo  ██║╚██╔╝██║██║██║     ██╔══██╗██║   ██║╚════██║██╔══╝  ██╔══██╗╚██╗ ██╔╝██║██║     ██╔══╝  ╚════██║
echo  ██║ ╚═╝ ██║██║╚██████╗██║  ██║╚██████╔╝███████║███████╗██║  ██║ ╚████╔╝ ██║╚██████╗███████╗███████║
echo  ╚═╝     ╚═╝╚═╝ ╚═════╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚═╝ ╚═════╝╚══════╝╚══════╝
echo.
echo                                    🚀 SMART LAUNCHER v2.0
echo                                   PHP + MySQL Microservices
echo.
echo ================================================================================
echo.

echo 🔍 STEP 1: Detecting available technologies...
echo =============================================
echo.

:: Check for Docker first (preferred method)
set DOCKER_AVAILABLE=0
set DOCKER_COMPOSE_AVAILABLE=0
set XAMPP_AVAILABLE=0
set PREFERRED_METHOD=""

echo 🐳 Checking for Docker...
docker --version >nul 2>&1
if %errorlevel% equ 0 (
    set DOCKER_AVAILABLE=1
    echo ✅ Docker: FOUND

    :: Check Docker Compose
    docker-compose --version >nul 2>&1
    if %errorlevel% equ 0 (
        set DOCKER_COMPOSE_AVAILABLE=1
        echo ✅ Docker Compose: FOUND
        set PREFERRED_METHOD=docker
    ) else (
        echo ⚠️ Docker Compose: NOT FOUND
    )
) else (
    echo ❌ Docker: NOT FOUND
)

echo.
echo 🔧 Checking for XAMPP...
if exist "C:\xampp\php\php.exe" (
    set XAMPP_AVAILABLE=1
    echo ✅ XAMPP: FOUND at C:\xampp\
    if "%PREFERRED_METHOD%"=="" set PREFERRED_METHOD=xampp
) else if exist "C:\XAMPP\php\php.exe" (
    set XAMPP_AVAILABLE=1
    echo ✅ XAMPP: FOUND at C:\XAMPP\
    if "%PREFERRED_METHOD%"=="" set PREFERRED_METHOD=xampp
) else if exist "%USERPROFILE%\xampp\php\php.exe" (
    set XAMPP_AVAILABLE=1
    echo ✅ XAMPP: FOUND at %USERPROFILE%\xampp\
    if "%PREFERRED_METHOD%"=="" set PREFERRED_METHOD=xampp
) else (
    echo ❌ XAMPP: NOT FOUND
)

echo.
echo 📊 DETECTION RESULTS:
echo ---------------------
echo Docker Available: %DOCKER_AVAILABLE%
echo Docker Compose Available: %DOCKER_COMPOSE_AVAILABLE%
echo XAMPP Available: %XAMPP_AVAILABLE%
echo Preferred Method: %PREFERRED_METHOD%
echo.

:: Check if system is already running
if exist ".running_docker" (
    echo ✅ System is running with Docker!
    echo.
    echo 📍 Services available at:
    echo   🔐 Auth Service: http://localhost:8001
    echo   👤 User Service: http://localhost:8002
    echo   🛍️ Product Service: http://localhost:8003
    echo   📦 Order Service: http://localhost:8004
    echo.
    echo 💡 To stop: docker-compose down
    pause
    exit /b 0
)

if exist ".running_xampp" (
    echo ✅ System is running with XAMPP!
    echo.
    echo 📍 Services available at:
    echo   🔐 Auth Service: http://localhost:8001
    echo   👤 User Service: http://localhost:8002
    echo   🛍️ Product Service: http://localhost:8003
    echo   📦 Order Service: http://localhost:8004
    echo.
    echo 💡 To stop: Close the service windows
    pause
    exit /b 0
)

echo 🎯 STEP 2: Choosing deployment method...
echo ========================================
echo.

:: Determine the best method
if %DOCKER_COMPOSE_AVAILABLE%==1 (
    echo 🐳 RECOMMENDED: Docker + Docker Compose
    echo   ✅ Professional deployment
    echo   ✅ Isolated containers
    echo   ✅ Easy scaling
    echo   ✅ Production-ready
    echo.

    if %XAMPP_AVAILABLE%==1 (
        echo 🔧 ALTERNATIVE: XAMPP
        echo   ✅ Simple setup
        echo   ✅ Local development
        echo   ✅ Direct file access
        echo.

        echo 🤔 Which method would you prefer?
        echo   [1] Docker (Recommended)
        echo   [2] XAMPP (Simple)
        echo.
        set /p METHOD_CHOICE="Enter your choice (1 or 2): "

        if "!METHOD_CHOICE!"=="1" (
            set CHOSEN_METHOD=docker
        ) else if "!METHOD_CHOICE!"=="2" (
            set CHOSEN_METHOD=xampp
        ) else (
            echo ❌ Invalid choice. Using Docker (default).
            set CHOSEN_METHOD=docker
        )
    ) else (
        echo 🚀 Using Docker (only option available)
        set CHOSEN_METHOD=docker
    )

) else if %XAMPP_AVAILABLE%==1 (
    echo 🔧 Using XAMPP (Docker not available)
    set CHOSEN_METHOD=xampp

) else (
    echo ❌ Neither Docker nor XAMPP found!
    echo.
    echo 💡 Available options:
    echo   [1] Install Docker Desktop (Recommended)
    echo   [2] Install XAMPP (Simple)
    echo.
    set /p INSTALL_CHOICE="What would you like to install? (1 or 2): "

    if "!INSTALL_CHOICE!"=="1" (
        echo.
        echo 🔽 Opening Docker Desktop download page...
        start https://www.docker.com/products/docker-desktop/
        echo.
        echo 📋 Please:
        echo   1. Download and install Docker Desktop
        echo   2. Restart your computer if prompted
        echo   3. Run this script again
        echo.
        pause
        exit /b 1
    ) else if "!INSTALL_CHOICE!"=="2" (
        set CHOSEN_METHOD=xampp_install
    ) else (
        echo ❌ Invalid choice. Exiting.
        pause
        exit /b 1
    )
)

echo.
echo 🎯 STEP 3: Starting deployment with %CHOSEN_METHOD%...
echo =====================================================
echo.

if "%CHOSEN_METHOD%"=="docker" (
    call :deploy_with_docker
) else if "%CHOSEN_METHOD%"=="xampp" (
    call :deploy_with_xampp
) else if "%CHOSEN_METHOD%"=="xampp_install" (
    call :install_xampp_and_deploy
)

exit /b 0

:: Function to deploy with Docker
:deploy_with_docker
echo 🐳 Deploying with Docker...
echo.

:: Check if docker-compose.yml exists
if not exist "docker-compose.yml" (
    echo ❌ docker-compose.yml not found!
    echo 💡 Make sure you're in the correct directory
    pause
    exit /b 1
)

echo 🔽 Pulling Docker images...
docker-compose pull

echo 🚀 Starting services...
docker-compose up -d

if %errorlevel% equ 0 (
    echo DOCKER_RUNNING=true > .running_docker
    echo START_TIME=%date% %time% >> .running_docker

    echo.
    echo ✅ Docker deployment successful!
    echo.
    echo 📍 Services are available at:
    echo   🌐 API Gateway: http://localhost:8000
    echo   🔐 Auth Service: http://localhost:8001
    echo   👤 User Service: http://localhost:8002
    echo   🛍️ Product Service: http://localhost:8003
    echo   📦 Order Service: http://localhost:8004
    echo.
    echo ⏳ Waiting for services to start...
    timeout /t 10 /nobreak >nul

    echo 🧪 Testing services...
    curl -s http://localhost:8000/health >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ API Gateway: Running
    ) else (
        echo ⚠️ API Gateway: Starting...
    )

    echo.
    echo 🎉 System is ready!
    echo 💡 To stop: docker-compose down

) else (
    echo ❌ Docker deployment failed!
    echo 💡 Check Docker Desktop is running
    pause
    exit /b 1
)
goto :eof

:: Function to deploy with XAMPP
:deploy_with_xampp
echo 🔧 Deploying with XAMPP...
echo.

if exist ".installed" (
    echo ✅ XAMPP system already configured
    echo 🚀 Starting services...
    call start_services_direct.bat
) else (
    echo 🔧 First time XAMPP setup...
    call install.bat
)

if %errorlevel% equ 0 (
    echo XAMPP_RUNNING=true > .running_xampp
    echo START_TIME=%date% %time% >> .running_xampp
    echo ✅ XAMPP deployment successful!
) else (
    echo ❌ XAMPP deployment failed!
    pause
    exit /b 1
)
goto :eof

:: Function to install XAMPP and deploy
:install_xampp_and_deploy
echo 📥 Installing XAMPP and deploying...
echo.
call install.bat
goto :eof
