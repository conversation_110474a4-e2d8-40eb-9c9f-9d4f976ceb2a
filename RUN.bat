@echo off
title Microservices - Quick Start
color 0B
cls

echo.
echo  ███╗   ███╗██╗ ██████╗██████╗  ██████╗ ███████╗███████╗██████╗ ██╗   ██╗██╗ ██████╗███████╗███████╗
echo  ████╗ ████║██║██╔════╝██╔══██╗██╔═══██╗██╔════╝██╔════╝██╔══██╗██║   ██║██║██╔════╝██╔════╝██╔════╝
echo  ██╔████╔██║██║██║     ██████╔╝██║   ██║███████╗█████╗  ██████╔╝██║   ██║██║██║     █████╗  ███████╗
echo  ██║╚██╔╝██║██║██║     ██╔══██╗██║   ██║╚════██║██╔══╝  ██╔══██╗╚██╗ ██╔╝██║██║     ██╔══╝  ╚════██║
echo  ██║ ╚═╝ ██║██║╚██████╗██║  ██║╚██████╔╝███████║███████╗██║  ██║ ╚████╔╝ ██║╚██████╗███████╗███████║
echo  ╚═╝     ╚═╝╚═╝ ╚═════╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚═╝ ╚═════╝╚══════╝╚══════╝
echo.
echo                                    🚀 QUICK START LAUNCHER
echo                                   PHP + MySQL Microservices
echo.
echo ================================================================================
echo.

:: Check if already installed
if exist ".installed" (
    echo ✅ System is installed and ready!
    echo.
    echo 🚀 Starting services...
    call start_services_direct.bat
    exit /b 0
)

echo 🎯 FIRST TIME SETUP
echo ===================
echo.
echo This appears to be your first time running the system.
echo The auto-installer will set everything up for you!
echo.
echo 📋 What will happen:
echo   1. 🔍 Check for XAMPP (download if needed)
echo   2. 🗄️ Setup database and tables
echo   3. ⚙️ Configure all services
echo   4. 🧪 Test the installation
echo   5. 🚀 Start all services
echo   6. 🔗 Create desktop shortcuts
echo.
echo ⏱️ This will take 2-5 minutes depending on your system.
echo.
echo 🤔 Ready to proceed?
echo.
set /p PROCEED="Press Y to continue or N to exit (Y/n): "

if /i "%PROCEED%"=="n" (
    echo.
    echo 👋 Setup cancelled. Run this file again when ready!
    pause
    exit /b 0
)

if /i "%PROCEED%"=="" set PROCEED=Y

if /i "%PROCEED%"=="y" (
    echo.
    echo 🚀 Starting auto-installation...
    echo.
    call install.bat
) else (
    echo.
    echo ❌ Invalid choice. Please run again and choose Y or N.
    pause
    exit /b 1
)
