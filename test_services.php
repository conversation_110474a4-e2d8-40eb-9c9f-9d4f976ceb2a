<?php
echo "🔍 اختبار حالة الخدمات\n";
echo "===================\n\n";

$services = [
    'Auth Service' => 'http://localhost:8001/health',
    'User Service' => 'http://localhost:8002/health', 
    'Product Service' => 'http://localhost:8003/health',
    'Order Service' => 'http://localhost:8004/health'
];

foreach ($services as $name => $url) {
    echo "🔍 اختبار $name...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ خطأ: $error\n";
    } elseif ($httpCode === 200) {
        echo "✅ يعمل بشكل طبيعي\n";
        echo "   الاستجابة: $response\n";
    } else {
        echo "⚠️ HTTP Code: $httpCode\n";
        echo "   الاستجابة: $response\n";
    }
    echo "\n";
}

echo "🧪 اختبار تسجيل الدخول...\n";
echo "========================\n";

$loginData = json_encode([
    'action' => 'login',
    'email' => '<EMAIL>',
    'password' => 'password'
]);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8001/');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $loginData);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($loginData)
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
if ($error) {
    echo "❌ خطأ: $error\n";
} else {
    echo "📝 الاستجابة: $response\n";
    
    $data = json_decode($response, true);
    if ($data && $data['status'] === 'success') {
        echo "✅ تسجيل الدخول يعمل بنجاح!\n";
    } else {
        echo "❌ مشكلة في تسجيل الدخول\n";
    }
}

echo "\n🎉 انتهى الاختبار\n";
?>
