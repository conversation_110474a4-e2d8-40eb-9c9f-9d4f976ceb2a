<?php
require_once 'config/ServiceRegistry.php';

class LoadBalancer {
    private $serviceRegistry;
    private $algorithm;
    private $roundRobinCounters;
    
    public function __construct($algorithm = 'round_robin') {
        $this->serviceRegistry = new ServiceRegistry();
        $this->algorithm = $algorithm;
        $this->roundRobinCounters = [];
    }
    
    public function getServiceUrl($serviceName) {
        $healthyServices = $this->serviceRegistry->getHealthyServices($serviceName);
        
        if (empty($healthyServices)) {
            return null;
        }
        
        switch ($this->algorithm) {
            case 'round_robin':
                return $this->roundRobin($serviceName, $healthyServices);
            case 'random':
                return $this->random($healthyServices);
            case 'least_connections':
                return $this->leastConnections($healthyServices);
            default:
                return $this->roundRobin($serviceName, $healthyServices);
        }
    }
    
    private function roundRobin($serviceName, $services) {
        if (!isset($this->roundRobinCounters[$serviceName])) {
            $this->roundRobinCounters[$serviceName] = 0;
        }
        
        $index = $this->roundRobinCounters[$serviceName] % count($services);
        $this->roundRobinCounters[$serviceName]++;
        
        return $services[$index];
    }
    
    private function random($services) {
        return $services[array_rand($services)];
    }
    
    private function leastConnections($services) {
        // Simplified implementation - in production, you'd track actual connections
        $connections = [];
        
        foreach ($services as $service) {
            $connections[$service] = $this->getActiveConnections($service);
        }
        
        return array_search(min($connections), $connections);
    }
    
    private function getActiveConnections($serviceUrl) {
        // Simplified - return random number for demo
        // In production, you'd track actual connections
        return rand(0, 10);
    }
    
    public function setAlgorithm($algorithm) {
        $this->algorithm = $algorithm;
    }
    
    public function getAlgorithm() {
        return $this->algorithm;
    }
}
