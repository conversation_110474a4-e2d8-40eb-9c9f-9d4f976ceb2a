@echo off
title Microservices System Updater
color 0E

echo.
echo 🔄 Microservices System Updater
echo ===============================
echo.

:: Check if system is installed
if not exist ".installed" (
    echo ❌ System not installed yet!
    echo 💡 Please run install.bat first
    pause
    exit /b 1
)

echo 🔍 Checking current installation...

:: Load current configuration
if exist ".config" (
    for /f "tokens=1,2 delims==" %%a in (.config) do (
        if "%%a"=="PHP_PATH" set PHP_PATH=%%b
    )
)

if %PHP_PATH%=="" (
    echo ❌ Configuration not found!
    echo 💡 Please run install.bat to reinstall
    pause
    exit /b 1
)

echo ✅ Current installation found
echo.

echo 🔄 Updating system components...
echo.

:: Update 1: Database Schema
echo 📋 Step 1: Checking database schema...
%PHP_PATH% -r "
try {
    \$pdo = new PDO('mysql:host=localhost;dbname=microservices_db', 'root', '');
    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check for new columns or tables
    \$tables = ['users', 'products', 'orders', 'order_items', 'notifications'];
    foreach (\$tables as \$table) {
        \$stmt = \$pdo->query(\"SHOW TABLES LIKE '\$table'\");
        if (\$stmt->rowCount() > 0) {
            echo \"✅ Table '\$table': OK\n\";
        } else {
            echo \"⚠️ Table '\$table': Missing\n\";
        }
    }
    
    // Check for refresh_token column in users table
    \$stmt = \$pdo->query(\"SHOW COLUMNS FROM users LIKE 'refresh_token'\");
    if (\$stmt->rowCount() > 0) {
        echo \"✅ refresh_token column: OK\n\";
    } else {
        echo \"🔧 Adding refresh_token column...\n\";
        \$pdo->exec(\"ALTER TABLE users ADD COLUMN refresh_token VARCHAR(500) AFTER token\");
        echo \"✅ refresh_token column: Added\n\";
    }
    
} catch (Exception \$e) {
    echo \"❌ Database check failed: \" . \$e->getMessage() . \"\n\";
    exit(1);
}
"
if %errorlevel% neq 0 (
    echo ❌ Database update failed!
    pause
    exit /b 1
)
echo.

:: Update 2: Configuration Files
echo ⚙️ Step 2: Updating configuration files...
if not exist ".env" (
    echo 🔧 Creating .env file...
    echo MYSQL_HOST=localhost > .env
    echo MYSQL_DATABASE=microservices_db >> .env
    echo MYSQL_USER=root >> .env
    echo MYSQL_PASSWORD= >> .env
    %PHP_PATH% -r "echo 'JWT_SECRET=' . bin2hex(random_bytes(32)) . \"\n\";" >> .env
    echo ✅ .env file created
) else (
    echo ✅ .env file exists
)

if not exist "config.json" (
    echo 🔧 Regenerating config.json...
    %PHP_PATH% -r "
    \$config = [
        'version' => '1.1.0',
        'updated' => date('Y-m-d H:i:s'),
        'services' => [
            'auth' => 'http://localhost:8001',
            'user' => 'http://localhost:8002',
            'product' => 'http://localhost:8003',
            'order' => 'http://localhost:8004'
        ]
    ];
    file_put_contents('config.json', json_encode(\$config, JSON_PRETTY_PRINT));
    echo \"✅ config.json updated\n\";
    "
) else (
    echo ✅ config.json exists
)
echo.

:: Update 3: Service Files
echo 🔧 Step 3: Checking service files...
set SERVICES_OK=1

if not exist "auth-service\utils\JWTHelper.php" (
    echo ❌ JWTHelper.php missing
    set SERVICES_OK=0
)

if not exist "shared\middleware\AuthMiddleware.php" (
    echo ❌ AuthMiddleware.php missing  
    set SERVICES_OK=0
)

if %SERVICES_OK%==1 (
    echo ✅ All service files present
) else (
    echo ⚠️ Some service files missing - consider reinstalling
)
echo.

:: Update 4: Test Installation
echo 🧪 Step 4: Testing updated system...
%PHP_PATH% test_installation.php
if %errorlevel% neq 0 (
    echo ❌ System test failed after update!
    echo 💡 Consider running install.bat to reinstall
    pause
    exit /b 1
)
echo.

:: Update 5: Update Version Info
echo 📝 Step 5: Updating version information...
echo UPDATED=true > .updated
echo UPDATE_DATE=%date% %time% >> .updated
echo VERSION=1.1.0 >> .updated

:: Update .installed file
echo INSTALLED=true > .installed
echo INSTALL_DATE=%date% %time% >> .installed
echo LAST_UPDATE=%date% %time% >> .installed
echo VERSION=1.1.0 >> .installed
echo PHP_PATH=%PHP_PATH% >> .installed

echo ✅ Version information updated
echo.

echo 🎉 SYSTEM UPDATE COMPLETED!
echo ============================
echo.
echo ✅ Update Summary:
echo   ✓ Database schema checked/updated
echo   ✓ Configuration files updated
echo   ✓ Service files verified
echo   ✓ System tests passed
echo   ✓ Version updated to 1.1.0
echo.
echo 🚀 System is ready to use!
echo.
echo 💡 Changes in this update:
echo   • Enhanced JWT authentication
echo   • Improved error handling
echo   • Better configuration management
echo   • Updated API documentation
echo.
echo 📍 Services will start automatically...
timeout /t 3 /nobreak >nul

:: Start services
call start_services_direct.bat

pause
