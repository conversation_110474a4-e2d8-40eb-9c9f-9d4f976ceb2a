<?php
/**
 * API Testing Script for Microservices
 * Run with: php tests/api_test.php
 */

class APITester {
    private $baseUrl;
    private $token;
    
    public function __construct($baseUrl = 'http://localhost:8000') {
        $this->baseUrl = $baseUrl;
    }
    
    public function runAllTests() {
        echo "🚀 Starting API Tests...\n\n";
        
        $this->testHealthChecks();
        $this->testAuthentication();
        $this->testUserService();
        $this->testProductService();
        $this->testOrderService();
        
        echo "\n✅ All tests completed!\n";
    }
    
    private function testHealthChecks() {
        echo "🏥 Testing Health Checks...\n";
        
        $services = [
            'API Gateway' => '/health',
            'Auth Service' => '/auth/health',
            'User Service' => '/users/health',
            'Product Service' => '/products/health',
            'Order Service' => '/orders/health'
        ];
        
        foreach ($services as $name => $endpoint) {
            $response = $this->makeRequest('GET', $endpoint);
            if ($response && isset($response['status']) && $response['status'] === 'healthy') {
                echo "  ✅ $name: Healthy\n";
            } else {
                echo "  ❌ $name: Unhealthy\n";
            }
        }
        echo "\n";
    }
    
    private function testAuthentication() {
        echo "🔐 Testing Authentication...\n";
        
        // Test Registration
        $registerData = [
            'action' => 'register',
            'name' => 'Test User',
            'email' => 'test_' . time() . '@example.com',
            'password' => 'password123'
        ];
        
        $response = $this->makeRequest('POST', '/auth', $registerData);
        if ($response && $response['status'] === 'success') {
            echo "  ✅ Registration: Success\n";
        } else {
            echo "  ❌ Registration: Failed\n";
            return;
        }
        
        // Test Login
        $loginData = [
            'action' => 'login',
            'email' => $registerData['email'],
            'password' => $registerData['password']
        ];
        
        $response = $this->makeRequest('POST', '/auth', $loginData);
        if ($response && $response['status'] === 'success' && isset($response['token'])) {
            $this->token = $response['token'];
            echo "  ✅ Login: Success\n";
        } else {
            echo "  ❌ Login: Failed\n";
            return;
        }
        
        // Test Token Verification
        $verifyData = [
            'action' => 'verify',
            'token' => $this->token
        ];
        
        $response = $this->makeRequest('POST', '/auth', $verifyData);
        if ($response && $response['status'] === 'success' && $response['valid']) {
            echo "  ✅ Token Verification: Success\n";
        } else {
            echo "  ❌ Token Verification: Failed\n";
        }
        
        echo "\n";
    }
    
    private function testUserService() {
        echo "👤 Testing User Service...\n";
        
        if (!$this->token) {
            echo "  ❌ No token available for user tests\n\n";
            return;
        }
        
        // Test Get User Profile
        $response = $this->makeRequest('GET', '/users?user_id=1', null, [
            'Authorization: Bearer ' . $this->token
        ]);
        
        if ($response && $response['status'] === 'success') {
            echo "  ✅ Get User Profile: Success\n";
        } else {
            echo "  ❌ Get User Profile: Failed\n";
        }
        
        echo "\n";
    }
    
    private function testProductService() {
        echo "🛍️ Testing Product Service...\n";
        
        // Test Get Products (no auth required)
        $response = $this->makeRequest('GET', '/products');
        if ($response && $response['status'] === 'success') {
            echo "  ✅ Get Products: Success\n";
        } else {
            echo "  ❌ Get Products: Failed\n";
        }
        
        // Test Get Single Product
        $response = $this->makeRequest('GET', '/products?product_id=1');
        if ($response && $response['status'] === 'success') {
            echo "  ✅ Get Single Product: Success\n";
        } else {
            echo "  ❌ Get Single Product: Failed\n";
        }
        
        if ($this->token) {
            // Test Create Product (auth required)
            $productData = [
                'name' => 'Test Product',
                'description' => 'Test Description',
                'price' => 99.99,
                'category' => 'Test',
                'stock' => 10
            ];
            
            $response = $this->makeRequest('POST', '/products', $productData, [
                'Authorization: Bearer ' . $this->token
            ]);
            
            if ($response && $response['status'] === 'success') {
                echo "  ✅ Create Product: Success\n";
            } else {
                echo "  ❌ Create Product: Failed\n";
            }
        }
        
        echo "\n";
    }
    
    private function testOrderService() {
        echo "📦 Testing Order Service...\n";
        
        if (!$this->token) {
            echo "  ❌ No token available for order tests\n\n";
            return;
        }
        
        // Test Create Order
        $orderData = [
            'items' => [
                [
                    'product_id' => 1,
                    'quantity' => 2
                ]
            ],
            'shipping_address' => 'Test Address',
            'payment_method' => 'cash'
        ];
        
        $response = $this->makeRequest('POST', '/orders', $orderData, [
            'Authorization: Bearer ' . $this->token
        ]);
        
        if ($response && $response['status'] === 'success') {
            echo "  ✅ Create Order: Success\n";
            $orderId = $response['order_id'];
            
            // Test Get Order
            $response = $this->makeRequest('GET', "/orders/$orderId", null, [
                'Authorization: Bearer ' . $this->token
            ]);
            
            if ($response && $response['status'] === 'success') {
                echo "  ✅ Get Order: Success\n";
            } else {
                echo "  ❌ Get Order: Failed\n";
            }
        } else {
            echo "  ❌ Create Order: Failed\n";
        }
        
        echo "\n";
    }
    
    private function makeRequest($method, $endpoint, $data = null, $headers = []) {
        $url = $this->baseUrl . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        
        $defaultHeaders = ['Content-Type: application/json'];
        $allHeaders = array_merge($defaultHeaders, $headers);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $allHeaders);
        
        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode >= 200 && $httpCode < 300) {
            return json_decode($response, true);
        }
        
        return null;
    }
}

// Run tests
$tester = new APITester();
$tester->runAllTests();
