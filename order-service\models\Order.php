<?php
class Order {
    private $conn;
    private $table = 'orders';
    private $itemsTable = 'order_items';

    public function __construct($db) {
        $this->conn = $db;
    }

    public function create($orderData, $orderItems) {
        try {
            $this->conn->beginTransaction();

            // Insert order
            $query = 'INSERT INTO ' . $this->table . ' 
                     (user_id, total_amount, status, shipping_address, payment_method) 
                     VALUES (:user_id, :total_amount, :status, :shipping_address, :payment_method)';
            
            $stmt = $this->conn->prepare($query);
            $stmt->execute([
                ':user_id' => $orderData['user_id'],
                ':total_amount' => $orderData['total_amount'],
                ':status' => $orderData['status'],
                ':shipping_address' => $orderData['shipping_address'],
                ':payment_method' => $orderData['payment_method']
            ]);

            $orderId = $this->conn->lastInsertId();

            // Insert order items
            $itemQuery = 'INSERT INTO ' . $this->itemsTable . ' 
                         (order_id, product_id, quantity, price, total) 
                         VALUES (:order_id, :product_id, :quantity, :price, :total)';
            
            $itemStmt = $this->conn->prepare($itemQuery);

            foreach ($orderItems as $item) {
                $itemStmt->execute([
                    ':order_id' => $orderId,
                    ':product_id' => $item['product_id'],
                    ':quantity' => $item['quantity'],
                    ':price' => $item['price'],
                    ':total' => $item['total']
                ]);
            }

            $this->conn->commit();
            return $orderId;

        } catch (Exception $e) {
            $this->conn->rollback();
            throw $e;
        }
    }

    public function getById($id, $userId = null) {
        $query = 'SELECT o.*, 
                         GROUP_CONCAT(
                             CONCAT(oi.product_id, ":", oi.quantity, ":", oi.price, ":", oi.total)
                             SEPARATOR ";"
                         ) as items
                  FROM ' . $this->table . ' o
                  LEFT JOIN ' . $this->itemsTable . ' oi ON o.id = oi.order_id
                  WHERE o.id = :id';
        
        $params = [':id' => $id];
        
        if ($userId) {
            $query .= ' AND o.user_id = :user_id';
            $params[':user_id'] = $userId;
        }
        
        $query .= ' GROUP BY o.id';
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        
        $order = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($order) {
            $order['items'] = $this->parseOrderItems($order['items']);
        }
        
        return $order;
    }

    public function getByUser($userId, $page = 1, $limit = 10, $status = null) {
        $offset = ($page - 1) * $limit;
        $params = [':user_id' => $userId];
        
        $query = 'SELECT * FROM ' . $this->table . ' WHERE user_id = :user_id';
        
        if ($status) {
            $query .= ' AND status = :status';
            $params[':status'] = $status;
        }
        
        $query .= ' ORDER BY created_at DESC LIMIT :limit OFFSET :offset';
        
        $stmt = $this->conn->prepare($query);
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function updateStatus($id, $userId, $status) {
        $query = 'UPDATE ' . $this->table . ' 
                  SET status = :status, updated_at = CURRENT_TIMESTAMP 
                  WHERE id = :id AND user_id = :user_id';
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([
            ':status' => $status,
            ':id' => $id,
            ':user_id' => $userId
        ]);
    }

    public function getOrderItems($orderId) {
        $query = 'SELECT * FROM ' . $this->itemsTable . ' WHERE order_id = :order_id';
        $stmt = $this->conn->prepare($query);
        $stmt->execute([':order_id' => $orderId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    private function parseOrderItems($itemsString) {
        if (!$itemsString) {
            return [];
        }

        $items = [];
        $itemParts = explode(';', $itemsString);
        
        foreach ($itemParts as $itemPart) {
            $parts = explode(':', $itemPart);
            if (count($parts) === 4) {
                $items[] = [
                    'product_id' => $parts[0],
                    'quantity' => $parts[1],
                    'price' => $parts[2],
                    'total' => $parts[3]
                ];
            }
        }
        
        return $items;
    }

    public function getOrderStats($userId = null) {
        $query = 'SELECT 
                    COUNT(*) as total_orders,
                    SUM(total_amount) as total_revenue,
                    AVG(total_amount) as average_order_value,
                    status,
                    COUNT(*) as status_count
                  FROM ' . $this->table;
        
        if ($userId) {
            $query .= ' WHERE user_id = :user_id';
        }
        
        $query .= ' GROUP BY status';
        
        $stmt = $this->conn->prepare($query);
        
        if ($userId) {
            $stmt->execute([':user_id' => $userId]);
        } else {
            $stmt->execute();
        }
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
