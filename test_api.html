<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار API - نظام إدارة الشركة</h1>
        
        <div>
            <button onclick="testAPI()">اختبار API</button>
            <button onclick="testDirect()">اختبار مباشر</button>
            <button onclick="clearResults()">مسح النتائج</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        function addResult(message, type = 'result') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testAPI() {
            addResult('🔄 بدء اختبار API...', 'result');
            
            try {
                const apiUrl = 'api/dashboard_data.php';
                addResult(`📡 محاولة الاتصال بـ: ${apiUrl}`, 'result');
                
                const response = await fetch(apiUrl);
                addResult(`📊 حالة الاستجابة: ${response.status} ${response.statusText}`, 
                         response.ok ? 'success' : 'error');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                addResult('✅ تم جلب البيانات بنجاح!', 'success');
                addResult(`📋 البيانات المستلمة:<br><pre>${JSON.stringify(data, null, 2)}</pre>`, 'result');
                
            } catch (error) {
                addResult(`❌ خطأ في الاتصال: ${error.message}`, 'error');
                addResult('💡 جاري المحاولة مع المسار المطلق...', 'result');
                
                try {
                    const absoluteUrl = 'http://localhost/haitham/api/dashboard_data.php';
                    addResult(`📡 محاولة الاتصال بـ: ${absoluteUrl}`, 'result');
                    
                    const response2 = await fetch(absoluteUrl);
                    addResult(`📊 حالة الاستجابة: ${response2.status} ${response2.statusText}`, 
                             response2.ok ? 'success' : 'error');
                    
                    if (!response2.ok) {
                        throw new Error(`HTTP error! status: ${response2.status}`);
                    }
                    
                    const data2 = await response2.json();
                    addResult('✅ تم جلب البيانات بنجاح من المسار المطلق!', 'success');
                    addResult(`📋 البيانات المستلمة:<br><pre>${JSON.stringify(data2, null, 2)}</pre>`, 'result');
                    
                } catch (error2) {
                    addResult(`❌ خطأ في المسار المطلق أيضاً: ${error2.message}`, 'error');
                    addResult('🔧 تأكد من تشغيل خادم Apache/XAMPP', 'error');
                }
            }
        }

        async function testDirect() {
            addResult('🔗 فتح API في نافذة جديدة...', 'result');
            
            // محاولة فتح API مباشرة
            const urls = [
                'api/dashboard_data.php',
                'http://localhost/haitham/api/dashboard_data.php'
            ];
            
            urls.forEach((url, index) => {
                addResult(`🌐 رابط ${index + 1}: <a href="${url}" target="_blank">${url}</a>`, 'result');
            });
        }

        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            addResult('🚀 مرحباً! اضغط على "اختبار API" لبدء الاختبار', 'result');
            addResult(`📍 الموقع الحالي: ${window.location.href}`, 'result');
            addResult(`🔧 البروتوكول: ${window.location.protocol}`, 'result');
        });
    </script>
</body>
</html>
