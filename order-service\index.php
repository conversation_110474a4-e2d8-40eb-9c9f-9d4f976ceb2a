<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Access-Control-Allow-Headers, Content-Type, Access-Control-Allow-Methods, Authorization, X-Requested-With');

require_once '../shared/middleware/AuthMiddleware.php';
require_once 'controllers/OrderController.php';

$auth = new AuthMiddleware();
$orderController = new OrderController();

$method = $_SERVER['REQUEST_METHOD'];
$uri = $_SERVER['REQUEST_URI'];

// Health check endpoint
if ($method === 'GET' && $uri === '/health') {
    echo json_encode(['status' => 'healthy', 'service' => 'order-service']);
    exit();
}

// Require authentication for all order operations
$userId = $auth->requireAuth();

// Parse URI to get order ID
$orderId = null;
if (preg_match('/\/orders\/(\d+)/', $uri, $matches)) {
    $orderId = $matches[1];
}

switch ($method) {
    case 'GET':
        if ($orderId) {
            echo $orderController->getOrder($orderId, $userId);
        } else {
            $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
            $status = isset($_GET['status']) ? $_GET['status'] : null;
            echo $orderController->listOrders($userId, $page, $limit, $status);
        }
        break;

    case 'POST':
        $data = json_decode(file_get_contents("php://input"), true);
        echo $orderController->createOrder($userId, $data);
        break;

    case 'PUT':
        if ($orderId) {
            $data = json_decode(file_get_contents("php://input"), true);
            echo $orderController->updateOrder($orderId, $userId, $data);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Order ID required']);
        }
        break;

    case 'DELETE':
        if ($orderId) {
            echo $orderController->cancelOrder($orderId, $userId);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Order ID required']);
        }
        break;

    default:
        echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
}
