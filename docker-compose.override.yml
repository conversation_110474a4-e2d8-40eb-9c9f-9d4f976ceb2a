# Development overrides for docker-compose.yml
# This file is automatically loaded by docker-compose

version: '3.8'

services:
  # API Gateway - Development settings
  api-gateway:
    environment:
      - APP_ENV=development
      - APP_DEBUG=true
      - LOG_LEVEL=debug
    volumes:
      - ./api-gateway:/var/www/html
      - ./shared:/var/www/html/shared
      - ./logs/api-gateway:/var/log/nginx

  # Auth Service - Development settings
  auth-service:
    environment:
      - APP_ENV=development
      - APP_DEBUG=true
      - LOG_LEVEL=debug
    volumes:
      - ./auth-service:/var/www/html
      - ./shared:/var/www/html/shared
      - ./logs/auth-service:/var/log/apache2

  # User Service - Development settings
  user-service:
    environment:
      - APP_ENV=development
      - APP_DEBUG=true
      - LOG_LEVEL=debug
    volumes:
      - ./user-service:/var/www/html
      - ./shared:/var/www/html/shared
      - ./logs/user-service:/var/log/apache2

  # Product Service - Development settings
  product-service:
    environment:
      - APP_ENV=development
      - APP_DEBUG=true
      - LOG_LEVEL=debug
    volumes:
      - ./product-service:/var/www/html
      - ./shared:/var/www/html/shared
      - ./logs/product-service:/var/log/apache2

  # Order Service - Development settings
  order-service:
    environment:
      - APP_ENV=development
      - APP_DEBUG=true
      - LOG_LEVEL=debug
    volumes:
      - ./order-service:/var/www/html
      - ./shared:/var/www/html/shared
      - ./logs/order-service:/var/log/apache2

  # Database - Development settings
  db:
    environment:
      - MYSQL_GENERAL_LOG=1
      - MYSQL_GENERAL_LOG_FILE=/var/lib/mysql/general.log
    volumes:
      - ./logs/mysql:/var/log/mysql
    command: --general-log=1 --general-log-file=/var/lib/mysql/general.log

  # Redis - Development settings
  redis:
    command: redis-server --loglevel verbose --logfile /data/redis.log
