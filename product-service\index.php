<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Access-Control-Allow-Headers, Content-Type, Access-Control-Allow-Methods, Authorization, X-Requested-With');

require_once 'config/Database.php';
require_once 'models/Product.php';
require_once 'controllers/ProductController.php';

$productController = new ProductController();

// Verify token (you should implement proper token verification)
$headers = getallheaders();
if (!isset($headers['Authorization'])) {
    echo json_encode(['status' => 'error', 'message' => 'Unauthorized']);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];
$productId = isset($_GET['id']) ? $_GET['id'] : null;

switch ($method) {
    case 'GET':
        if ($productId) {
            echo $productController->getProduct($productId);
        } else {
            $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
            $category = isset($_GET['category']) ? $_GET['category'] : null;
            echo $productController->listProducts($page, $limit, $category);
        }
        break;

    case 'POST':
        $data = json_decode(file_get_contents("php://input"), true);
        echo $productController->create($data);
        break;

    case 'PUT':
        if ($productId) {
            $data = json_decode(file_get_contents("php://input"), true);
            echo $productController->updateProduct($productId, $data);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Product ID required']);
        }
        break;

    case 'DELETE':
        if ($productId) {
            echo $productController->deleteProduct($productId);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Product ID required']);
        }
        break;

    default:
        echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
}
