<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Access-Control-Allow-Headers, Content-Type, Access-Control-Allow-Methods, Authorization, X-Requested-With');

require_once '../shared/middleware/AuthMiddleware.php';
require_once 'config/Database.php';
require_once 'models/Product.php';
require_once 'controllers/ProductController.php';

$auth = new AuthMiddleware();
$productController = new ProductController();

$method = $_SERVER['REQUEST_METHOD'];
$uri = $_SERVER['REQUEST_URI'];

// Health check endpoint
if ($method === 'GET' && $uri === '/health') {
    echo json_encode(['status' => 'healthy', 'service' => 'product-service']);
    exit();
}

// For GET requests (viewing products), authentication is optional
// For POST/PUT/DELETE (managing products), authentication is required
if (in_array($method, ['POST', 'PUT', 'DELETE'])) {
    $currentUserId = $auth->requireAuth();
} else {
    $currentUserId = $auth->optionalAuth();
}

$productId = isset($_GET['id']) ? $_GET['id'] : null;

switch ($method) {
    case 'GET':
        if ($productId) {
            echo $productController->getProduct($productId);
        } else {
            $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
            $category = isset($_GET['category']) ? $_GET['category'] : null;
            echo $productController->listProducts($page, $limit, $category);
        }
        break;

    case 'POST':
        $data = json_decode(file_get_contents("php://input"), true);
        echo $productController->create($data);
        break;

    case 'PUT':
        if ($productId) {
            $data = json_decode(file_get_contents("php://input"), true);

            // Check if this is a stock update request
            if (strpos($uri, '/stock') !== false && isset($data['quantity_change'])) {
                echo $productController->updateStock($productId, $data['quantity_change']);
            } else {
                echo $productController->updateProduct($productId, $data);
            }
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Product ID required']);
        }
        break;

    case 'DELETE':
        if ($productId) {
            echo $productController->deleteProduct($productId);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Product ID required']);
        }
        break;

    default:
        echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
}
