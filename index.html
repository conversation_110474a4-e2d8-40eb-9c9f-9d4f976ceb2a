<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المايكروسيرفس - الصفحة الرئيسية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
        }

        .header h1 {
            font-size: 48px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 20px;
            opacity: 0.9;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .feature-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .feature-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 24px;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        .cta-section {
            text-align: center;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .cta-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #667eea;
        }

        .btn-secondary:hover {
            background: #667eea;
            color: white;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .stat-item {
            text-align: center;
            color: white;
        }

        .stat-number {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 16px;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 36px;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 نظام المايكروسيرفس</h1>
            <p>نظام إدارة متطور مبني بتقنية المايكروسيرفس</p>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">4</div>
                <div class="stat-label">خدمات متكاملة</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">99.9%</div>
                <div class="stat-label">وقت التشغيل</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">24/7</div>
                <div class="stat-label">دعم مستمر</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">100%</div>
                <div class="stat-label">آمان البيانات</div>
            </div>
        </div>

        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">🔐</div>
                <h3>نظام مصادقة آمن</h3>
                <p>نظام مصادقة متطور باستخدام JWT tokens مع تشفير عالي الأمان وإدارة جلسات المستخدمين</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">👥</div>
                <h3>إدارة المستخدمين</h3>
                <p>إدارة شاملة للمستخدمين مع إمكانية تحديث البيانات وإدارة الصلاحيات والأدوار المختلفة</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">⚙️</div>
                <h3>إدارة النظام</h3>
                <p>لوحة تحكم متقدمة لمراقبة حالة النظام والخدمات مع إحصائيات مفصلة وتقارير شاملة</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <h3>تحليلات وتقارير</h3>
                <p>نظام تحليلات متطور يوفر إحصائيات مفصلة وتقارير دورية لمساعدتك في اتخاذ القرارات</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🔄</div>
                <h3>تحديثات تلقائية</h3>
                <p>النظام يحدث نفسه تلقائياً ويراقب حالة الخدمات باستمرار لضمان الأداء الأمثل</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <h3>واجهة متجاوبة</h3>
                <p>تصميم متجاوب يعمل على جميع الأجهزة مع واجهة مستخدم سهلة وبديهية باللغة العربية</p>
            </div>
        </div>

        <div class="cta-section">
            <h2>ابدأ استخدام النظام الآن</h2>
            <p>انضم إلى آلاف المستخدمين الذين يثقون في نظامنا لإدارة أعمالهم</p>
            
            <div class="cta-buttons">
                <a href="login.html" class="btn btn-primary">تسجيل الدخول</a>
                <a href="register.html" class="btn btn-secondary">إنشاء حساب جديد</a>
                <a href="dashboard.html" class="btn btn-secondary">عرض النظام</a>
            </div>
        </div>
    </div>

    <script>
        // Check if user is already logged in
        if (localStorage.getItem('authToken')) {
            // Add a "Go to Dashboard" button if logged in
            const ctaButtons = document.querySelector('.cta-buttons');
            ctaButtons.innerHTML = `
                <a href="dashboard.html" class="btn btn-primary">الذهاب للوحة التحكم</a>
                <a href="#" class="btn btn-secondary" onclick="logout()">تسجيل الخروج</a>
            `;
        }

        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('userId');
            location.reload();
        }

        // Animate stats on page load
        window.addEventListener('load', function() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalValue = stat.textContent;
                stat.textContent = '0';
                
                if (finalValue.includes('%')) {
                    animateNumber(stat, 0, parseFloat(finalValue), '%');
                } else if (finalValue.includes('/')) {
                    stat.textContent = finalValue; // Don't animate fractions
                } else {
                    animateNumber(stat, 0, parseInt(finalValue));
                }
            });
        });

        function animateNumber(element, start, end, suffix = '') {
            const duration = 2000;
            const increment = (end - start) / (duration / 16);
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if (current >= end) {
                    current = end;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current) + suffix;
            }, 16);
        }
    </script>
</body>
</html>
