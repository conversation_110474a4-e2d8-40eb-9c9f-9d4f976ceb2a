<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Access-Control-Allow-Headers, Content-Type, Access-Control-Allow-Methods, Authorization, X-Requested-With');

require_once 'controllers/AuthController.php';

$auth = new AuthController();

$data = json_decode(file_get_contents("php://input"));

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($data->action)) {
        switch ($data->action) {
            case 'login':
                if (isset($data->email) && isset($data->password)) {
                    echo $auth->login($data->email, $data->password);
                } else {
                    echo json_encode(['status' => 'error', 'message' => 'Missing required fields']);
                }
                break;
            
            case 'register':
                if (isset($data->name) && isset($data->email) && isset($data->password)) {
                    echo $auth->register($data->name, $data->email, $data->password);
                } else {
                    echo json_encode(['status' => 'error', 'message' => 'Missing required fields']);
                }
                break;
            
            default:
                echo json_encode(['status' => 'error', 'message' => 'Invalid action']);
        }
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Action not specified']);
    }
} else {
    echo json_encode(['status' => 'error', 'message' => 'Invalid request method']);
}
