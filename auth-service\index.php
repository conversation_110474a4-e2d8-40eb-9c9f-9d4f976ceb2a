<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Access-Control-Allow-Headers, Content-Type, Access-Control-Allow-Methods, Authorization, X-Requested-With');

require_once 'controllers/AuthController.php';

$auth = new AuthController();

$method = $_SERVER['REQUEST_METHOD'];
$data = json_decode(file_get_contents("php://input"));

// Health check endpoint
if ($method === 'GET' && $_SERVER['REQUEST_URI'] === '/health') {
    echo json_encode(['status' => 'healthy', 'service' => 'auth-service']);
    exit();
}

if ($method === 'POST') {
    if (isset($data->action)) {
        switch ($data->action) {
            case 'login':
                if (isset($data->email) && isset($data->password)) {
                    echo $auth->login($data->email, $data->password);
                } else {
                    echo json_encode(['status' => 'error', 'message' => 'Missing required fields']);
                }
                break;

            case 'register':
                if (isset($data->name) && isset($data->email) && isset($data->password)) {
                    echo $auth->register($data->name, $data->email, $data->password);
                } else {
                    echo json_encode(['status' => 'error', 'message' => 'Missing required fields']);
                }
                break;

            case 'refresh':
                if (isset($data->refresh_token)) {
                    echo $auth->refreshToken($data->refresh_token);
                } else {
                    echo json_encode(['status' => 'error', 'message' => 'Refresh token required']);
                }
                break;

            case 'logout':
                $headers = getallheaders();
                if (isset($headers['Authorization'])) {
                    $token = str_replace('Bearer ', '', $headers['Authorization']);
                    try {
                        $jwtHelper = new JWTHelper();
                        $decoded = $jwtHelper->decode($token);
                        echo $auth->logout($decoded['user_id']);
                    } catch (Exception $e) {
                        echo json_encode(['status' => 'error', 'message' => 'Invalid token']);
                    }
                } else {
                    echo json_encode(['status' => 'error', 'message' => 'Authorization header required']);
                }
                break;

            case 'verify':
                if (isset($data->token)) {
                    echo $auth->verifyToken($data->token);
                } else {
                    echo json_encode(['status' => 'error', 'message' => 'Token required']);
                }
                break;

            default:
                echo json_encode(['status' => 'error', 'message' => 'Invalid action']);
        }
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Action not specified']);
    }
} else {
    echo json_encode(['status' => 'error', 'message' => 'Invalid request method']);
}
