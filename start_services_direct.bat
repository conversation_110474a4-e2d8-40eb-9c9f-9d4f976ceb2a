@echo off
title Microservices Launcher
color 0A

:: Check if system is installed
if not exist ".installed" (
    echo ⚠️ System not installed yet!
    echo.
    echo 🚀 Running auto-installer...
    call install.bat
    exit /b 0
)

echo.
echo 🚀 Starting Microservices System
echo ================================
echo.

:: Load configuration
if exist ".config" (
    for /f "tokens=1,2 delims==" %%a in (.config) do (
        if "%%a"=="PHP_PATH" set PHP_PATH=%%b
    )
) else (
    :: Find PHP in XAMPP
    set PHP_PATH=""
    if exist "C:\xampp\php\php.exe" set PHP_PATH="C:\xampp\php\php.exe"
    if exist "C:\XAMPP\php\php.exe" set PHP_PATH="C:\XAMPP\php\php.exe"
    if exist "%USERPROFILE%\xampp\php\php.exe" set PHP_PATH="%USERPROFILE%\xampp\php\php.exe"
)

if %PHP_PATH%=="" (
    echo ❌ PHP not found! Please run install.bat first
    pause
    exit /b 1
)

echo ✅ Using PHP: %PHP_PATH%
echo.

:: Check MySQL
netstat -an | findstr :3306 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL not running! Start it in XAMPP Control Panel
    pause
    exit /b 1
)
echo ✅ MySQL is running
echo.

:: Start services
echo 🔐 Starting Auth Service (Port 8001)...
start "🔐 Auth Service" cmd /k "title Auth Service ^& echo Auth Service: http://localhost:8001 ^& %PHP_PATH% -S localhost:8001 -t auth-service"

timeout /t 2 /nobreak >nul

echo 👤 Starting User Service (Port 8002)...
start "👤 User Service" cmd /k "title User Service ^& echo User Service: http://localhost:8002 ^& %PHP_PATH% -S localhost:8002 -t user-service"

timeout /t 2 /nobreak >nul

echo 🛍️ Starting Product Service (Port 8003)...
start "🛍️ Product Service" cmd /k "title Product Service ^& echo Product Service: http://localhost:8003 ^& %PHP_PATH% -S localhost:8003 -t product-service"

timeout /t 2 /nobreak >nul

echo 📦 Starting Order Service (Port 8004)...
start "📦 Order Service" cmd /k "title Order Service ^& echo Order Service: http://localhost:8004 ^& %PHP_PATH% -S localhost:8004 -t order-service"

echo.
echo ⏳ Waiting for services to initialize...
timeout /t 8 /nobreak >nul

echo.
echo ✅ All services should be running now!
echo.
echo 📍 Service URLs:
echo    🔐 Auth:    http://localhost:8001
echo    👤 User:    http://localhost:8002  
echo    🛍️ Product: http://localhost:8003
echo    📦 Order:   http://localhost:8004
echo.
echo 🧪 Quick test commands:
echo    curl http://localhost:8001/health
echo    curl http://localhost:8002/health
echo    curl http://localhost:8003/health
echo    curl http://localhost:8004/health
echo.
echo 📋 Full API test:
echo    %PHP_PATH% tests/api_test_xampp.php
echo.
echo 💡 Tips:
echo    - Each service runs in its own window
echo    - Close service windows to stop them
echo    - Check XAMPP Control Panel for MySQL
echo    - Use Postman for API testing
echo.
echo 📚 Documentation: README_XAMPP.md
echo.
pause
