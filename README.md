# 🚀 نظام المايكروسيرفس - PHP + MySQL

نظام متكامل للمايكروسيرفس مع **تثبيت تلقائي ذكي** يقوم بإعداد نفسه في أول مرة تشغيل.

## ✨ الميزات الجديدة

- 🔄 **تثبيت تلقائي كامل** - خطوة واحدة فقط!
- 🔍 **كشف تلقائي لـ XAMPP** - يجد ويثبت XAMPP تلقائياً
- 🗄️ **إعداد قاعدة البيانات** - جداول وبيانات تجريبية
- 🧪 **اختبار شامل** - يتأكد من سلامة التثبيت
- 🔗 **اختصارات سطح المكتب** - تشغيل بنقرة واحدة

## 🎯 التشغيل الذكي (خطوة واحدة!)

```bash
# انقر مرتين على الملف - هذا كل شيء!
RUN.bat
```

**🧠 النظام الذكي سيقوم بـ:**
1. 🔍 **فحص Docker** - يتحقق من وجود Docker أولاً
2. 🔧 **فحص XAMPP** - كبديل إذا لم يوجد Docker
3. 🎯 **اختيار الأفضل** - Docker للأداء، XAMPP للبساطة
4. 🚀 **تشغيل تلقائي** - يبدأ النظام بالطريقة المناسبة
5. 📊 **مراقبة ذكية** - يتابع حالة جميع الخدمات

## 🔄 إدارة النظام

```bash
RUN.bat     # تشغيل ذكي
STOP.bat    # إيقاف ذكي
STATUS.bat  # فحص الحالة
```

## 🏗️ البنية المعمارية

### الخدمات (Services)
- **Auth Service** (Port 8001) - خدمة المصادقة والتحقق
- **User Service** (Port 8002) - إدارة المستخدمين
- **Product Service** (Port 8003) - إدارة المنتجات
- **Order Service** (Port 8004) - إدارة الطلبات

### قواعد البيانات والتخزين
- **MySQL** (XAMPP) - قاعدة البيانات الرئيسية
- **JWT Authentication** - نظام مصادقة آمن

## 📍 معلومات الوصول (بعد التثبيت)

### الخدمات
- 🔐 **Auth Service**: http://localhost:8001
- 👤 **User Service**: http://localhost:8002
- 🛍️ **Product Service**: http://localhost:8003
- 📦 **Order Service**: http://localhost:8004

### المستخدم الإداري
- 📧 **Email**: <EMAIL>
- 🔑 **Password**: admin123

## 🔄 طرق التشغيل البديلة

### مع Docker (للمطورين المتقدمين)
```bash
docker-compose up -d
```

### مع XAMPP (الطريقة المبسطة)
```bash
# التثبيت التلقائي
install.bat

# أو التشغيل المباشر (إذا كان مثبتاً)
start_services_direct.bat
```

## 📡 API Endpoints

### 🔐 Authentication Service

#### تسجيل الدخول
```bash
POST http://localhost:8000/auth
Content-Type: application/json

{
    "action": "login",
    "email": "<EMAIL>",
    "password": "password"
}
```

#### إنشاء حساب جديد
```bash
POST http://localhost:8000/auth
Content-Type: application/json

{
    "action": "register",
    "name": "اسم المستخدم",
    "email": "<EMAIL>",
    "password": "password"
}
```

#### تجديد الـ Token
```bash
POST http://localhost:8000/auth
Content-Type: application/json

{
    "action": "refresh",
    "refresh_token": "your_refresh_token"
}
```

#### تسجيل الخروج
```bash
POST http://localhost:8000/auth
Authorization: Bearer your_jwt_token
Content-Type: application/json

{
    "action": "logout"
}
```

### 👤 User Service

#### عرض ملف المستخدم
```bash
GET http://localhost:8000/users?user_id=1
Authorization: Bearer your_jwt_token
```

#### تحديث ملف المستخدم
```bash
PUT http://localhost:8000/users?user_id=1
Authorization: Bearer your_jwt_token
Content-Type: application/json

{
    "name": "الاسم الجديد",
    "email": "<EMAIL>"
}
```

### 🛍️ Product Service

#### عرض جميع المنتجات
```bash
GET http://localhost:8000/products?page=1&limit=10&category=Electronics
```

#### عرض منتج محدد
```bash
GET http://localhost:8000/products?product_id=1
```

#### إضافة منتج جديد (يتطلب مصادقة)
```bash
POST http://localhost:8000/products
Authorization: Bearer your_jwt_token
Content-Type: application/json

{
    "name": "منتج جديد",
    "description": "وصف المنتج",
    "price": 99.99,
    "category": "Electronics",
    "stock": 100
}
```

#### تحديث منتج (يتطلب مصادقة)
```bash
PUT http://localhost:8000/products?product_id=1
Authorization: Bearer your_jwt_token
Content-Type: application/json

{
    "name": "اسم محدث",
    "price": 89.99,
    "stock": 150
}
```

### 📦 Order Service

#### إنشاء طلب جديد
```bash
POST http://localhost:8000/orders
Authorization: Bearer your_jwt_token
Content-Type: application/json

{
    "items": [
        {
            "product_id": 1,
            "quantity": 2
        },
        {
            "product_id": 2,
            "quantity": 1
        }
    ],
    "shipping_address": "عنوان التوصيل",
    "payment_method": "cash"
}
```

#### عرض طلبات المستخدم
```bash
GET http://localhost:8000/orders?page=1&limit=10&status=pending
Authorization: Bearer your_jwt_token
```

#### عرض طلب محدد
```bash
GET http://localhost:8000/orders/1
Authorization: Bearer your_jwt_token
```

#### تحديث حالة الطلب
```bash
PUT http://localhost:8000/orders/1
Authorization: Bearer your_jwt_token
Content-Type: application/json

{
    "status": "confirmed"
}
```

#### إلغاء طلب
```bash
DELETE http://localhost:8000/orders/1
Authorization: Bearer your_jwt_token
```

## 🔧 الميزات المتقدمة

### 🛡️ الأمان
- **JWT Tokens** مع Refresh Tokens
- **Rate Limiting** (100 طلب/ساعة لكل IP)
- **Authentication Middleware** مشترك
- **CORS** مُفعل لجميع الخدمات

### ⚖️ Load Balancing
- **Round Robin** (افتراضي)
- **Random**
- **Least Connections**

### 📊 Monitoring & Health Checks
- Health check endpoints لجميع الخدمات
- Service discovery تلقائي
- Logging مركزي

### 🗄️ قاعدة البيانات
- **MySQL 8.0** مع بيانات تجريبية
- **Redis** للتخزين المؤقت
- **Foreign Keys** و **Indexes** محسنة

## 🔧 التطوير

### إضافة خدمة جديدة

1. إنشاء مجلد الخدمة
2. إضافة Dockerfile
3. تحديث docker-compose.yml
4. إضافة routing في API Gateway

### متغيرات البيئة

```env
# Database
MYSQL_HOST=db
MYSQL_USER=root
MYSQL_PASSWORD=root
MYSQL_DATABASE=microservices_db

# JWT
JWT_SECRET=your-super-secret-jwt-key

# Services
AUTH_SERVICE_URL=http://auth-service
PRODUCT_SERVICE_URL=http://product-service

# Redis
REDIS_HOST=redis
REDIS_PORT=6379

# Notifications
EMAIL_NOTIFICATIONS=true
SMS_NOTIFICATIONS=false
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في الاتصال بقاعدة البيانات**
```bash
docker-compose logs db
```

2. **مشاكل في الـ Authentication**
```bash
docker-compose logs auth-service
```

3. **مشاكل في الـ Rate Limiting**
```bash
docker-compose logs redis
```

### إعادة تشغيل خدمة معينة
```bash
docker-compose restart auth-service
```

### عرض logs خدمة معينة
```bash
docker-compose logs -f order-service
```

## 📈 الأداء

- **Rate Limiting**: 100 طلب/ساعة
- **Database Connection Pooling**: مُفعل
- **Redis Caching**: مُفعل
- **Load Balancing**: Round Robin

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء branch جديد
3. إضافة التحسينات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.
