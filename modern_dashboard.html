<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم العصرية - نظام الحماية المتقدم</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap');
        
        :root {
            --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark: #1a1a2e;
            --darker: #16213e;
            --light: #ffffff;
            --text-light: #f8f9fa;
            --text-muted: #6c757d;
            --glass: rgba(255, 255, 255, 0.1);
            --shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            color: var(--text-light);
            direction: rtl;
            overflow-x: hidden;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(120deg); }
            66% { transform: translateY(30px) rotate(240deg); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 25px 35px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--shadow);
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 60px;
            height: 60px;
            background: var(--primary);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .title-section h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .title-section p {
            color: var(--text-muted);
            font-size: 14px;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            background: var(--success);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 18px;
            color: white;
        }

        .logout-btn {
            background: var(--danger);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(250, 112, 154, 0.4);
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 30px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(31, 38, 135, 0.5);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 4px;
            background: var(--primary);
        }

        .stat-card.success::before { background: var(--success); }
        .stat-card.warning::before { background: var(--warning); }
        .stat-card.danger::before { background: var(--danger); }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            background: var(--primary);
            color: white;
        }

        .stat-icon.success { background: var(--success); }
        .stat-icon.warning { background: var(--warning); }
        .stat-icon.danger { background: var(--danger); }

        .stat-value {
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 8px;
            background: var(--primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            color: var(--text-muted);
            font-size: 16px;
            font-weight: 500;
        }

        /* Main Content Grid */
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .card-header {
            padding: 25px 30px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 20px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .card-body {
            padding: 30px;
        }

        /* Service Status */
        .service-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .service-item:last-child {
            border-bottom: none;
        }

        .service-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .service-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4facfe;
            box-shadow: 0 0 10px rgba(79, 172, 254, 0.6);
        }

        .service-status.warning {
            background: #43e97b;
            box-shadow: 0 0 10px rgba(67, 233, 123, 0.6);
        }

        .service-status.danger {
            background: #fa709a;
            box-shadow: 0 0 10px rgba(250, 112, 154, 0.6);
        }

        /* Buttons */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-warning {
            background: var(--warning);
            color: white;
        }

        .btn-danger {
            background: var(--danger);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        /* Security Log */
        .security-log {
            max-height: 350px;
            overflow-y: auto;
        }

        .log-entry {
            padding: 15px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-time {
            color: var(--text-muted);
            font-size: 12px;
            margin-top: 5px;
        }

        /* Alert */
        .alert {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border-right: 4px solid #4facfe;
        }

        .alert.success { border-right-color: #43e97b; }
        .alert.warning { border-right-color: #ffa726; }
        .alert.danger { border-right-color: #fa709a; }

        /* Tools Section */
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            
            .tools-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo-section">
                <div class="logo">🛡️</div>
                <div class="title-section">
                    <h1>نظام الحماية المتقدم</h1>
                    <p>لوحة التحكم الإدارية</p>
                </div>
            </div>
            <div class="user-section">
                <div>
                    <div style="font-weight: 600;">مرحباً، المدير</div>
                    <div style="font-size: 12px; color: var(--text-muted);">آخر دخول: منذ 5 دقائق</div>
                </div>
                <div class="user-avatar">م</div>
                <button class="logout-btn" onclick="logout()">
                    تسجيل الخروج
                </button>
            </div>
        </div>

        <!-- Alert Container -->
        <div id="alert-container"></div>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card success">
                <div class="stat-header">
                    <div class="stat-icon success">🛡️</div>
                </div>
                <div class="stat-value">98%</div>
                <div class="stat-label">مستوى الأمان</div>
            </div>

            <div class="stat-card warning">
                <div class="stat-header">
                    <div class="stat-icon warning">⚠️</div>
                </div>
                <div class="stat-value">3</div>
                <div class="stat-label">تهديدات محتملة</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon">👥</div>
                </div>
                <div class="stat-value" id="activeUsers">24</div>
                <div class="stat-label">مستخدمين نشطين</div>
            </div>

            <div class="stat-card success">
                <div class="stat-header">
                    <div class="stat-icon success">⚡</div>
                </div>
                <div class="stat-value">99.9%</div>
                <div class="stat-label">وقت التشغيل</div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="content-grid">
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        ⚙️ حالة الخدمات
                    </div>
                    <button class="btn btn-primary" onclick="refreshServices()">
                        🔄 تحديث
                    </button>
                </div>
                <div class="card-body">
                    <div class="service-item">
                        <div class="service-info">
                            <div class="service-status"></div>
                            <div>
                                <div style="font-weight: 600;">خدمة المصادقة</div>
                                <div class="log-time">آخر فحص: منذ دقيقة</div>
                            </div>
                        </div>
                        <span style="color: #4facfe; font-weight: 600;">✅ متصل</span>
                    </div>
                    <div class="service-item">
                        <div class="service-info">
                            <div class="service-status"></div>
                            <div>
                                <div style="font-weight: 600;">خدمة المستخدمين</div>
                                <div class="log-time">آخر فحص: منذ دقيقة</div>
                            </div>
                        </div>
                        <span style="color: #4facfe; font-weight: 600;">✅ متصل</span>
                    </div>
                    <div class="service-item">
                        <div class="service-info">
                            <div class="service-status warning"></div>
                            <div>
                                <div style="font-weight: 600;">قاعدة البيانات</div>
                                <div class="log-time">آخر فحص: منذ 3 دقائق</div>
                            </div>
                        </div>
                        <span style="color: #43e97b; font-weight: 600;">⚡ بطيء</span>
                    </div>
                    <div class="service-item">
                        <div class="service-info">
                            <div class="service-status"></div>
                            <div>
                                <div style="font-weight: 600;">خدمة الأمان</div>
                                <div class="log-time">آخر فحص: منذ 30 ثانية</div>
                            </div>
                        </div>
                        <span style="color: #4facfe; font-weight: 600;">🔒 محمي</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        🔒 سجل الأمان
                    </div>
                </div>
                <div class="card-body">
                    <div class="security-log">
                        <div class="log-entry">
                            <div>✅ محاولة دخول ناجحة من IP: *************</div>
                            <div class="log-time">منذ 5 دقائق</div>
                        </div>
                        <div class="log-entry">
                            <div style="color: #ffa726;">⚠️ محاولة دخول مشبوهة من IP: ************</div>
                            <div class="log-time">منذ 15 دقيقة</div>
                        </div>
                        <div class="log-entry">
                            <div>🔑 تحديث كلمة مرور للمستخدم: admin</div>
                            <div class="log-time">منذ ساعة</div>
                        </div>
                        <div class="log-entry">
                            <div style="color: #fa709a;">🚨 محاولة اختراق محجوبة</div>
                            <div class="log-time">منذ ساعتين</div>
                        </div>
                        <div class="log-entry">
                            <div>💾 نسخ احتياطي تلقائي مكتمل</div>
                            <div class="log-time">منذ 3 ساعات</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tools Section -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">
                    🛠️ أدوات الإدارة السريعة
                </div>
            </div>
            <div class="card-body">
                <div class="tools-grid">
                    <button class="btn btn-primary" onclick="runSecurityScan()">
                        🔍 فحص أمني شامل
                    </button>
                    <button class="btn btn-success" onclick="createBackup()">
                        💾 نسخة احتياطية
                    </button>
                    <button class="btn btn-warning" onclick="updateSystem()">
                        🔄 تحديث النظام
                    </button>
                    <button class="btn btn-danger" onclick="emergencyLock()">
                        🔒 قفل طوارئ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthentication();
            startRealTimeUpdates();
            showWelcomeMessage();
        });

        function checkAuthentication() {
            const token = localStorage.getItem('authToken');
            if (!token) {
                window.location.href = 'login.html';
                return;
            }
        }

        function showWelcomeMessage() {
            showAlert('🎉 مرحباً بك في نظام الحماية المتقدم. جميع الأنظمة تعمل بشكل طبيعي.', 'success');
        }

        function startRealTimeUpdates() {
            setInterval(updateStats, 30000);
        }

        function updateStats() {
            const activeUsers = Math.floor(Math.random() * 10) + 20;
            document.getElementById('activeUsers').textContent = activeUsers;
        }

        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alert-container');
            const alert = document.createElement('div');
            alert.className = `alert ${type}`;
            alert.innerHTML = message;
            
            alertContainer.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        function refreshServices() {
            showAlert('🔄 جاري تحديث حالة الخدمات...', 'warning');
            setTimeout(() => {
                showAlert('✅ تم تحديث حالة جميع الخدمات بنجاح', 'success');
            }, 2000);
        }

        function runSecurityScan() {
            showAlert('🔍 بدء الفحص الأمني الشامل...', 'warning');
            setTimeout(() => {
                showAlert('🛡️ اكتمل الفحص الأمني. لم يتم العثور على تهديدات', 'success');
            }, 5000);
        }

        function createBackup() {
            showAlert('💾 جاري إنشاء النسخة الاحتياطية...', 'warning');
            setTimeout(() => {
                showAlert('✅ تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            }, 3000);
        }

        function updateSystem() {
            showAlert('🔄 جاري البحث عن التحديثات...', 'warning');
            setTimeout(() => {
                showAlert('✅ النظام محدث إلى أحدث إصدار', 'success');
            }, 4000);
        }

        function emergencyLock() {
            if (confirm('🚨 هل أنت متأكد من تفعيل قفل الطوارئ؟ سيتم منع جميع العمليات.')) {
                showAlert('🔒 تم تفعيل قفل الطوارئ. جميع العمليات متوقفة.', 'danger');
            }
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('authToken');
                localStorage.removeItem('refreshToken');
                localStorage.removeItem('userId');
                window.location.href = 'modern_login.html';
            }
        }
    </script>
</body>
</html>
