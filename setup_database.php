<?php
// ملف إعداد قاعدة البيانات
echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إعداد قاعدة البيانات</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #27ae60; background: #d4edda; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".error { color: #e74c3c; background: #f8d7da; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".info { color: #3498db; background: #d1ecf1; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo "h1 { color: #2c3e50; }";
echo "h2 { color: #34495e; margin-top: 30px; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";
echo "<h1>🚀 إعداد قاعدة البيانات - نظام إدارة الشركة</h1>";

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'company_management';

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    echo "<h2>📡 الاتصال بخادم MySQL...</h2>";
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='success'>✅ تم الاتصال بخادم MySQL بنجاح</div>";
    
    // إنشاء قاعدة البيانات
    echo "<h2>🗄️ إنشاء قاعدة البيانات...</h2>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $dbname CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<div class='success'>✅ تم إنشاء قاعدة البيانات '$dbname' بنجاح</div>";
    
    // الاتصال بقاعدة البيانات الجديدة
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // قراءة ملف SQL وتنفيذه
    echo "<h2>📋 إنشاء الجداول وإدراج البيانات...</h2>";
    $sqlFile = 'database_setup.sql';
    
    if (file_exists($sqlFile)) {
        $sql = file_get_contents($sqlFile);
        
        // تقسيم الاستعلامات
        $queries = explode(';', $sql);
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query) && !preg_match('/^--/', $query)) {
                try {
                    $pdo->exec($query);
                } catch (PDOException $e) {
                    // تجاهل أخطاء الجداول الموجودة
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        echo "<div class='error'>❌ خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "</div>";
                    }
                }
            }
        }
        
        echo "<div class='success'>✅ تم إنشاء جميع الجداول وإدراج البيانات التجريبية بنجاح</div>";
    } else {
        echo "<div class='error'>❌ لم يتم العثور على ملف database_setup.sql</div>";
    }
    
    // التحقق من البيانات
    echo "<h2>🔍 التحقق من البيانات...</h2>";
    
    $tables = ['users', 'projects', 'tasks', 'system_services', 'activity_log', 'system_metrics'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            echo "<div class='info'>📊 جدول '$table': $count سجل</div>";
        } catch (PDOException $e) {
            echo "<div class='error'>❌ خطأ في قراءة جدول '$table': " . $e->getMessage() . "</div>";
        }
    }
    
    echo "<h2>🎉 تم الإعداد بنجاح!</h2>";
    echo "<div class='success'>";
    echo "<strong>✅ تم إعداد قاعدة البيانات بنجاح!</strong><br><br>";
    echo "<strong>📋 معلومات الاتصال:</strong><br>";
    echo "• الخادم: $host<br>";
    echo "• قاعدة البيانات: $dbname<br>";
    echo "• المستخدم: $username<br><br>";
    echo "<strong>👤 بيانات تسجيل الدخول:</strong><br>";
    echo "• البريد الإلكتروني: <EMAIL><br>";
    echo "• كلمة المرور: password<br><br>";
    echo "<a href='login.html' style='background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🚀 انتقل إلى صفحة الدخول</a>";
    echo "</div>";
    
} catch(PDOException $e) {
    echo "<div class='error'>";
    echo "<strong>❌ خطأ في الاتصال بقاعدة البيانات:</strong><br>";
    echo $e->getMessage() . "<br><br>";
    echo "<strong>🔧 تأكد من:</strong><br>";
    echo "• تشغيل خادم MySQL<br>";
    echo "• صحة بيانات الاتصال<br>";
    echo "• وجود صلاحيات إنشاء قواعد البيانات<br>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
