<?php
class ServiceRegistry {
    private $services;
    
    public function __construct() {
        $this->services = [
            'auth-service' => [
                'urls' => [
                    'http://auth-service:80'
                ],
                'health_check' => '/health'
            ],
            'user-service' => [
                'urls' => [
                    'http://user-service:80'
                ],
                'health_check' => '/health'
            ],
            'product-service' => [
                'urls' => [
                    'http://product-service:80'
                ],
                'health_check' => '/health'
            ],
            'order-service' => [
                'urls' => [
                    'http://order-service:80'
                ],
                'health_check' => '/health'
            ]
        ];
    }
    
    public function getService($serviceName) {
        return isset($this->services[$serviceName]) ? $this->services[$serviceName] : null;
    }
    
    public function getAllServices() {
        return $this->services;
    }
    
    public function isServiceHealthy($serviceUrl, $healthCheckPath = '/health') {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $serviceUrl . $healthCheckPath);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return $httpCode === 200;
    }
    
    public function getHealthyServices($serviceName) {
        $service = $this->getService($serviceName);
        if (!$service) {
            return [];
        }
        
        $healthyUrls = [];
        foreach ($service['urls'] as $url) {
            if ($this->isServiceHealthy($url, $service['health_check'])) {
                $healthyUrls[] = $url;
            }
        }
        
        return $healthyUrls;
    }
}
