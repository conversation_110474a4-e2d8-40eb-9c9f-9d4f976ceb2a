<?php
// ملف تشخيص مشاكل API
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تشخيص API</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }";
echo ".success { color: #27ae60; background: #d4edda; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo ".error { color: #e74c3c; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo ".info { color: #3498db; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo ".warning { color: #f39c12; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 5px 0; }";
echo "pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";
echo "<h1>🔍 تشخيص API - نظام إدارة الشركة</h1>";

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'company_management';
$username = 'root';
$password = '';

echo "<h2>📊 اختبار الاتصال بقاعدة البيانات</h2>";

try {
    echo "<div class='info'>🔄 محاولة الاتصال بـ: mysql:host=$host;dbname=$dbname</div>";
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح!</div>";
    
    // اختبار الجداول
    echo "<h3>📋 فحص الجداول المطلوبة:</h3>";
    
    $tables = ['users', 'projects', 'tasks', 'system_services', 'activity_log', 'system_metrics'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            echo "<div class='success'>✅ جدول '$table': $count سجل</div>";
        } catch (PDOException $e) {
            echo "<div class='error'>❌ جدول '$table': غير موجود - " . $e->getMessage() . "</div>";
        }
    }
    
    // اختبار الاستعلامات المحددة
    echo "<h3>🔍 اختبار الاستعلامات:</h3>";
    
    // جلب إحصائيات المستخدمين
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total_users FROM users WHERE status = 'active'");
        $totalUsers = $stmt->fetch()['total_users'];
        echo "<div class='success'>👥 إجمالي المستخدمين النشطين: $totalUsers</div>";
    } catch (PDOException $e) {
        echo "<div class='error'>❌ خطأ في جلب المستخدمين: " . $e->getMessage() . "</div>";
    }
    
    // جلب المشاريع النشطة
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as active_projects FROM projects WHERE status = 'active'");
        $activeProjects = $stmt->fetch()['active_projects'];
        echo "<div class='success'>📋 المشاريع النشطة: $activeProjects</div>";
    } catch (PDOException $e) {
        echo "<div class='error'>❌ خطأ في جلب المشاريع: " . $e->getMessage() . "</div>";
    }
    
    // جلب المهام المعلقة
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as pending_tasks FROM tasks WHERE status = 'pending'");
        $pendingTasks = $stmt->fetch()['pending_tasks'];
        echo "<div class='success'>⏳ المهام المعلقة: $pendingTasks</div>";
    } catch (PDOException $e) {
        echo "<div class='error'>❌ خطأ في جلب المهام: " . $e->getMessage() . "</div>";
    }
    
    // اختبار API مباشرة
    echo "<h3>🌐 اختبار API مباشرة:</h3>";
    
    // تشغيل نفس كود API
    $stmt = $pdo->query("SELECT COUNT(*) as total_users FROM users WHERE status = 'active'");
    $totalUsers = $stmt->fetch()['total_users'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as active_projects FROM projects WHERE status = 'active'");
    $activeProjects = $stmt->fetch()['active_projects'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as pending_tasks FROM tasks WHERE status = 'pending'");
    $pendingTasks = $stmt->fetch()['pending_tasks'];
    
    $stmt = $pdo->query("SELECT AVG(performance_score) as avg_performance FROM system_metrics WHERE DATE(created_at) = CURDATE()");
    $performance = $stmt->fetch()['avg_performance'] ?? 99.5;
    
    $response = [
        'success' => true,
        'stats' => [
            'total_users' => (int)$totalUsers,
            'active_projects' => (int)$activeProjects,
            'pending_tasks' => (int)$pendingTasks,
            'performance' => round($performance, 1),
            'system_efficiency' => 98.5,
            'response_time' => 2.3,
            'uptime' => 99.9
        ]
    ];
    
    echo "<div class='success'>📊 استجابة API:</div>";
    echo "<pre>" . json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    
    // اختبار تفصيلي للمستخدمين
    echo "<h3>👥 تفاصيل المستخدمين:</h3>";
    $stmt = $pdo->query("SELECT id, username, full_name, role, status, created_at FROM users ORDER BY created_at DESC LIMIT 10");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='info'>📋 آخر 10 مستخدمين:</div>";
    foreach ($users as $user) {
        echo "<div class='info'>• {$user['full_name']} ({$user['username']}) - {$user['role']} - {$user['status']} - {$user['created_at']}</div>";
    }
    
} catch(PDOException $e) {
    echo "<div class='error'>❌ خطأ في الاتصال بقاعدة البيانات:</div>";
    echo "<div class='error'>الخطأ: " . $e->getMessage() . "</div>";
    echo "<div class='warning'>🔧 تأكد من:</div>";
    echo "<div class='warning'>• تشغيل خادم MySQL (XAMPP/WAMP)</div>";
    echo "<div class='warning'>• وجود قاعدة البيانات 'company_management'</div>";
    echo "<div class='warning'>• صحة بيانات الاتصال</div>";
    
    echo "<h3>🛠️ خطوات الإصلاح:</h3>";
    echo "<div class='info'>1. تشغيل XAMPP Control Panel</div>";
    echo "<div class='info'>2. تشغيل Apache و MySQL</div>";
    echo "<div class='info'>3. تشغيل <a href='setup_database.php'>setup_database.php</a></div>";
    echo "<div class='info'>4. تشغيل <a href='add_more_users.php'>add_more_users.php</a></div>";
}

echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<div class='info'>";
echo "• <a href='setup_database.php'>إعداد قاعدة البيانات</a><br>";
echo "• <a href='add_more_users.php'>إضافة مستخدمين</a><br>";
echo "• <a href='api/dashboard_data.php'>اختبار API مباشرة</a><br>";
echo "• <a href='test_api.html'>صفحة اختبار API</a><br>";
echo "• <a href='dashboard.html'>لوحة التحكم</a><br>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
