<?php
echo "🔐 Testing Login System\n";
echo "======================\n\n";

// Test 1: Health Check
echo "1. Testing Auth Service Health...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8001/health');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    echo "✅ Auth Service is healthy\n";
} else {
    echo "❌ Auth Service health check failed (HTTP $httpCode)\n";
    echo "Response: $response\n";
}

echo "\n";

// Test 2: Login with admin user
echo "2. Testing Login with admin user...\n";

$loginData = [
    'action' => 'login',
    'email' => '<EMAIL>',
    'password' => 'password'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8001/');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen(json_encode($loginData))
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
if ($error) {
    echo "CURL Error: $error\n";
}
echo "Response: $response\n";

if ($httpCode === 200) {
    $data = json_decode($response, true);
    if ($data && $data['status'] === 'success') {
        echo "✅ Login successful!\n";
        echo "🔑 Token: " . substr($data['token'], 0, 50) . "...\n";
        echo "👤 User ID: " . $data['user_id'] . "\n";
        
        // Save token for further tests
        file_put_contents('token.txt', $data['token']);
        echo "💾 Token saved to token.txt\n";
    } else {
        echo "❌ Login failed: " . ($data['message'] ?? 'Unknown error') . "\n";
    }
} else {
    echo "❌ HTTP request failed\n";
}

echo "\n";

// Test 3: Register new user
echo "3. Testing User Registration...\n";

$registerData = [
    'action' => 'register',
    'name' => 'Test User ' . time(),
    'email' => 'test' . time() . '@example.com',
    'password' => 'testpassword123'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8001/');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($registerData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen(json_encode($registerData))
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: $response\n";

if ($httpCode === 200) {
    $data = json_decode($response, true);
    if ($data && $data['status'] === 'success') {
        echo "✅ Registration successful!\n";
        echo "📧 Email: " . $registerData['email'] . "\n";
        echo "🔑 Password: " . $registerData['password'] . "\n";
    } else {
        echo "❌ Registration failed: " . ($data['message'] ?? 'Unknown error') . "\n";
    }
} else {
    echo "❌ HTTP request failed\n";
}

echo "\n🎉 Test completed!\n";
echo "\n💡 How to login manually:\n";
echo "Method: POST\n";
echo "URL: http://localhost:8001/\n";
echo "Headers: Content-Type: application/json\n";
echo "Body: {\n";
echo '  "action": "login",'."\n";
echo '  "email": "<EMAIL>",'."\n";
echo '  "password": "password"'."\n";
echo "}\n";
?>
