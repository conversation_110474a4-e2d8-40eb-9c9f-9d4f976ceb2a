# Production overrides for docker-compose.yml
# Use with: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

version: '3.8'

services:
  # API Gateway - Production settings
  api-gateway:
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - LOG_LEVEL=error
      - RATE_LIMIT_MAX_REQUESTS=1000
    restart: unless-stopped
    volumes:
      - ./api-gateway:/var/www/html:ro
      - ./shared:/var/www/html/shared:ro
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Auth Service - Production settings
  auth-service:
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - LOG_LEVEL=error
    restart: unless-stopped
    volumes:
      - ./auth-service:/var/www/html:ro
      - ./shared:/var/www/html/shared:ro
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # User Service - Production settings
  user-service:
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - LOG_LEVEL=error
    restart: unless-stopped
    volumes:
      - ./user-service:/var/www/html:ro
      - ./shared:/var/www/html/shared:ro
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Product Service - Production settings
  product-service:
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - LOG_LEVEL=error
    restart: unless-stopped
    volumes:
      - ./product-service:/var/www/html:ro
      - ./shared:/var/www/html/shared:ro
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Order Service - Production settings
  order-service:
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - LOG_LEVEL=error
    restart: unless-stopped
    volumes:
      - ./order-service:/var/www/html:ro
      - ./shared:/var/www/html/shared:ro
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Database - Production settings
  db:
    restart: unless-stopped
    environment:
      - MYSQL_GENERAL_LOG=0
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    command: >
      --default-authentication-plugin=mysql_native_password
      --innodb-buffer-pool-size=1G
      --innodb-log-file-size=256M
      --max-connections=200
      --query-cache-type=1
      --query-cache-size=64M

  # Redis - Production settings
  redis:
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    command: >
      redis-server
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
