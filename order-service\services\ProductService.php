<?php
class ProductService {
    private $productServiceUrl;
    
    public function __construct() {
        $this->productServiceUrl = getenv('PRODUCT_SERVICE_URL') ?: 'http://product-service';
    }
    
    public function getProduct($productId) {
        $url = $this->productServiceUrl . '/products/' . $productId;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            $result = json_decode($response, true);
            if ($result && $result['status'] === 'success') {
                return $result['data'];
            }
        }
        
        return null;
    }
    
    public function updateStock($productId, $quantityChange) {
        $url = $this->productServiceUrl . '/products/' . $productId . '/stock';
        
        $data = json_encode(['quantity_change' => $quantityChange]);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return $httpCode === 200;
    }
    
    public function checkAvailability($productId, $quantity) {
        $product = $this->getProduct($productId);
        
        if (!$product) {
            return false;
        }
        
        return $product['stock'] >= $quantity;
    }
    
    public function getMultipleProducts($productIds) {
        $products = [];
        
        foreach ($productIds as $productId) {
            $product = $this->getProduct($productId);
            if ($product) {
                $products[$productId] = $product;
            }
        }
        
        return $products;
    }
}
