<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الإدارية - نظام الحماية المتقدم</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1a1a2e;
            --secondary-color: #16213e;
            --accent-color: #0f3460;
            --success-color: #00d4aa;
            --warning-color: #ffa726;
            --danger-color: #ef5350;
            --text-light: #ffffff;
            --text-muted: #b0bec5;
            --card-bg: #1e1e2e;
            --border-color: #2a2a3e;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--text-light);
            min-height: 100vh;
            direction: rtl;
        }

        .sidebar {
            position: fixed;
            right: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: var(--card-bg);
            border-left: 1px solid var(--border-color);
            padding: 20px 0;
            z-index: 1000;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 0 20px 30px;
            border-bottom: 1px solid var(--border-color);
            text-align: center;
        }

        .logo {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--success-color);
        }

        .nav-menu {
            list-style: none;
            padding: 20px 0;
        }

        .nav-item {
            margin: 5px 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: var(--text-muted);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .nav-link:hover,
        .nav-link.active {
            background: var(--accent-color);
            color: var(--text-light);
            border-right-color: var(--success-color);
        }

        .nav-link i {
            margin-left: 15px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            margin-right: 280px;
            padding: 20px;
        }

        .top-bar {
            background: var(--card-bg);
            padding: 20px 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid var(--border-color);
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--success-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: var(--card-bg);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: var(--success-color);
        }

        .stat-card.warning::before {
            background: var(--warning-color);
        }

        .stat-card.danger::before {
            background: var(--danger-color);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            background: rgba(0, 212, 170, 0.1);
            color: var(--success-color);
        }

        .stat-icon.warning {
            background: rgba(255, 167, 38, 0.1);
            color: var(--warning-color);
        }

        .stat-icon.danger {
            background: rgba(239, 83, 80, 0.1);
            color: var(--danger-color);
        }

        .stat-value {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--text-muted);
            font-size: 14px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: var(--card-bg);
            border-radius: 15px;
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .card-header {
            padding: 20px 25px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-body {
            padding: 25px;
        }

        .service-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .service-item:last-child {
            border-bottom: none;
        }

        .service-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .service-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--success-color);
        }

        .service-status.warning {
            background: var(--warning-color);
        }

        .service-status.danger {
            background: var(--danger-color);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--success-color);
            color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #00b894;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: var(--accent-color);
            color: var(--text-light);
        }

        .btn-danger {
            background: var(--danger-color);
            color: var(--text-light);
        }

        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }

        .alert-success {
            background: rgba(0, 212, 170, 0.1);
            border-left-color: var(--success-color);
            color: var(--success-color);
        }

        .alert-warning {
            background: rgba(255, 167, 38, 0.1);
            border-left-color: var(--warning-color);
            color: var(--warning-color);
        }

        .alert-danger {
            background: rgba(239, 83, 80, 0.1);
            border-left-color: var(--danger-color);
            color: var(--danger-color);
        }

        .security-log {
            max-height: 300px;
            overflow-y: auto;
        }

        .log-entry {
            padding: 10px 0;
            border-bottom: 1px solid var(--border-color);
            font-size: 14px;
        }

        .log-time {
            color: var(--text-muted);
            font-size: 12px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--border-color);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: var(--success-color);
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .content-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="logo">🛡️</div>
            <div class="sidebar-title">نظام الحماية المتقدم</div>
        </div>
        
        <ul class="nav-menu">
            <li class="nav-item">
                <a href="#" class="nav-link active" onclick="showSection('dashboard')">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" onclick="showSection('security')">
                    <i class="fas fa-shield-alt"></i>
                    الأمان والحماية
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" onclick="showSection('users')">
                    <i class="fas fa-users"></i>
                    إدارة المستخدمين
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" onclick="showSection('services')">
                    <i class="fas fa-cogs"></i>
                    إدارة الخدمات
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" onclick="showSection('monitoring')">
                    <i class="fas fa-chart-line"></i>
                    المراقبة والتحليل
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" onclick="showSection('logs')">
                    <i class="fas fa-file-alt"></i>
                    سجلات النظام
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" onclick="showSection('settings')">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="top-bar">
            <div class="page-title">
                <i class="fas fa-tachometer-alt"></i>
                لوحة التحكم الرئيسية
            </div>
            <div class="user-info">
                <span>مرحباً، المدير</span>
                <div class="user-avatar">A</div>
                <button class="btn btn-danger" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    خروج
                </button>
            </div>
        </div>

        <div id="alert-container"></div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                </div>
                <div class="stat-value" id="securityLevel">98%</div>
                <div class="stat-label">مستوى الأمان</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 98%"></div>
                </div>
            </div>

            <div class="stat-card warning">
                <div class="stat-header">
                    <div class="stat-icon warning">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="stat-value" id="threats">3</div>
                <div class="stat-label">تهديدات محتملة</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="stat-value" id="activeUsers">24</div>
                <div class="stat-label">مستخدمين نشطين</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-server"></i>
                    </div>
                </div>
                <div class="stat-value" id="uptime">99.9%</div>
                <div class="stat-label">وقت التشغيل</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 99.9%"></div>
                </div>
            </div>
        </div>

        <div class="content-grid">
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-cogs"></i>
                        حالة الخدمات
                    </div>
                    <button class="btn btn-primary" onclick="refreshServices()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
                <div class="card-body">
                    <div id="services-list">
                        <div class="service-item">
                            <div class="service-info">
                                <div class="service-status"></div>
                                <div>
                                    <div>خدمة المصادقة</div>
                                    <div class="log-time">آخر فحص: منذ دقيقة</div>
                                </div>
                            </div>
                            <span style="color: var(--success-color);">متصل</span>
                        </div>
                        <div class="service-item">
                            <div class="service-info">
                                <div class="service-status"></div>
                                <div>
                                    <div>خدمة المستخدمين</div>
                                    <div class="log-time">آخر فحص: منذ دقيقة</div>
                                </div>
                            </div>
                            <span style="color: var(--success-color);">متصل</span>
                        </div>
                        <div class="service-item">
                            <div class="service-info">
                                <div class="service-status warning"></div>
                                <div>
                                    <div>خدمة قاعدة البيانات</div>
                                    <div class="log-time">آخر فحص: منذ 3 دقائق</div>
                                </div>
                            </div>
                            <span style="color: var(--warning-color);">بطيء</span>
                        </div>
                        <div class="service-item">
                            <div class="service-info">
                                <div class="service-status"></div>
                                <div>
                                    <div>خدمة الأمان</div>
                                    <div class="log-time">آخر فحص: منذ 30 ثانية</div>
                                </div>
                            </div>
                            <span style="color: var(--success-color);">محمي</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-shield-alt"></i>
                        سجل الأمان
                    </div>
                </div>
                <div class="card-body">
                    <div class="security-log">
                        <div class="log-entry">
                            <div>محاولة دخول ناجحة من IP: *************</div>
                            <div class="log-time">منذ 5 دقائق</div>
                        </div>
                        <div class="log-entry">
                            <div style="color: var(--warning-color);">محاولة دخول مشبوهة من IP: ************</div>
                            <div class="log-time">منذ 15 دقيقة</div>
                        </div>
                        <div class="log-entry">
                            <div>تحديث كلمة مرور للمستخدم: admin</div>
                            <div class="log-time">منذ ساعة</div>
                        </div>
                        <div class="log-entry">
                            <div style="color: var(--danger-color);">محاولة اختراق محجوبة</div>
                            <div class="log-time">منذ ساعتين</div>
                        </div>
                        <div class="log-entry">
                            <div>نسخ احتياطي تلقائي مكتمل</div>
                            <div class="log-time">منذ 3 ساعات</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-tools"></i>
                    أدوات الإدارة السريعة
                </div>
            </div>
            <div class="card-body">
                <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                    <button class="btn btn-primary" onclick="runSecurityScan()">
                        <i class="fas fa-search"></i>
                        فحص أمني شامل
                    </button>
                    <button class="btn btn-secondary" onclick="createBackup()">
                        <i class="fas fa-download"></i>
                        نسخة احتياطية
                    </button>
                    <button class="btn btn-secondary" onclick="updateSystem()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث النظام
                    </button>
                    <button class="btn btn-danger" onclick="emergencyLock()">
                        <i class="fas fa-lock"></i>
                        قفل طوارئ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthentication();
            startRealTimeUpdates();
            showWelcomeMessage();
        });

        function checkAuthentication() {
            const token = localStorage.getItem('authToken');
            if (!token) {
                window.location.href = 'login.html';
                return;
            }
        }

        function showWelcomeMessage() {
            showAlert('مرحباً بك في نظام الحماية المتقدم. جميع الأنظمة تعمل بشكل طبيعي.', 'success');
        }

        function startRealTimeUpdates() {
            // Update stats every 30 seconds
            setInterval(updateStats, 30000);
            
            // Update security log every 60 seconds
            setInterval(updateSecurityLog, 60000);
        }

        function updateStats() {
            // Simulate real-time data updates
            document.getElementById('activeUsers').textContent = Math.floor(Math.random() * 10) + 20;
            
            const threats = Math.floor(Math.random() * 5);
            document.getElementById('threats').textContent = threats;
            
            // Update security level
            const securityLevel = Math.floor(Math.random() * 5) + 95;
            document.getElementById('securityLevel').textContent = securityLevel + '%';
        }

        function showAlert(message, type) {
            const alertContainer = document.getElementById('alert-container');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'times-circle'}"></i>
                ${message}
            `;
            
            alertContainer.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        function refreshServices() {
            showAlert('جاري تحديث حالة الخدمات...', 'success');
            // Simulate service refresh
            setTimeout(() => {
                showAlert('تم تحديث حالة جميع الخدمات بنجاح', 'success');
            }, 2000);
        }

        function runSecurityScan() {
            showAlert('بدء الفحص الأمني الشامل...', 'warning');
            setTimeout(() => {
                showAlert('اكتمل الفحص الأمني. لم يتم العثور على تهديدات', 'success');
            }, 5000);
        }

        function createBackup() {
            showAlert('جاري إنشاء النسخة الاحتياطية...', 'success');
            setTimeout(() => {
                showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            }, 3000);
        }

        function updateSystem() {
            showAlert('جاري البحث عن التحديثات...', 'success');
            setTimeout(() => {
                showAlert('النظام محدث إلى أحدث إصدار', 'success');
            }, 4000);
        }

        function emergencyLock() {
            if (confirm('هل أنت متأكد من تفعيل قفل الطوارئ؟ سيتم منع جميع العمليات.')) {
                showAlert('تم تفعيل قفل الطوارئ. جميع العمليات متوقفة.', 'danger');
            }
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('authToken');
                localStorage.removeItem('refreshToken');
                localStorage.removeItem('userId');
                window.location.href = 'login.html';
            }
        }

        function showSection(section) {
            // Update active nav link
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Show appropriate content based on section
            showAlert(`تم التبديل إلى قسم: ${section}`, 'success');
        }

        function updateSecurityLog() {
            // Add new security log entry
            const logEntries = [
                'فحص أمني دوري مكتمل',
                'تحديث قاعدة بيانات التهديدات',
                'مراقبة حركة المرور الشبكية',
                'فحص سلامة النظام'
            ];
            
            const randomEntry = logEntries[Math.floor(Math.random() * logEntries.length)];
            // This would add to the security log in a real implementation
        }
    </script>
</body>
</html>
