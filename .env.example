# Database Configuration
MYSQL_HOST=db
MYSQL_USER=root
MYSQL_PASSWORD=root
MYSQL_DATABASE=microservices_db

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# Service URLs
AUTH_SERVICE_URL=http://auth-service
USER_SERVICE_URL=http://user-service
PRODUCT_SERVICE_URL=http://product-service
ORDER_SERVICE_URL=http://order-service
NOTIFICATION_SERVICE_URL=http://notification-service

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379

# Notification Settings
EMAIL_NOTIFICATIONS=true
SMS_NOTIFICATIONS=false

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_TIME_WINDOW=3600

# Load Balancer Algorithm
# Options: round_robin, random, least_connections
LOAD_BALANCER_ALGORITHM=round_robin

# Environment
APP_ENV=development
APP_DEBUG=true

# Logging
LOG_LEVEL=info
LOG_FILE=/var/log/microservices.log
