<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام المايكروسيرفس</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23ffffff" stroke-width="0.1" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            z-index: -1;
        }

        .login-container {
            background: rgba(30, 30, 46, 0.95);
            backdrop-filter: blur(20px);
            padding: 50px;
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 450px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 212, 170, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
            border-radius: 25px;
            z-index: -1;
        }

        .logo {
            font-size: 64px;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #00d4aa 0%, #667eea 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .title {
            color: #ffffff;
            margin-bottom: 35px;
            font-size: 28px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            color: #b0bec5;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 10px;
            color: #ffffff;
            font-weight: 600;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            direction: ltr;
            text-align: left;
            background: rgba(255, 255, 255, 0.05);
            color: #ffffff;
            backdrop-filter: blur(10px);
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-group input:focus {
            outline: none;
            border-color: #00d4aa;
            background: rgba(255, 255, 255, 0.1);
            box-shadow: 0 0 20px rgba(0, 212, 170, 0.3);
        }

        .login-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #00d4aa 0%, #667eea 100%);
            color: #1a1a2e;
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 25px;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 212, 170, 0.4);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .register-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .register-link:hover {
            text-decoration: underline;
        }

        .alert {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            display: none;
            margin: 10px 0;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .demo-users {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: right;
        }

        .demo-users h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .demo-user {
            background: white;
            padding: 8px;
            margin: 5px 0;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.2s;
            font-size: 14px;
        }

        .demo-user:hover {
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">🛡️</div>
        <h1 class="title">نظام الحماية المتقدم</h1>
        <p class="subtitle">تسجيل دخول آمن ومشفر</p>
        
        <div id="alert" class="alert" style="display: none;"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">البريد الإلكتروني:</label>
                <input type="email" id="email" name="email" required placeholder="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required placeholder="password">
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">
                دخول
            </button>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>جاري تسجيل الدخول...</p>
            </div>
        </form>
        
        <p>
            ليس لديك حساب؟ 
            <a href="#" class="register-link" onclick="showRegister()">إنشاء حساب جديد</a>
        </p>
        
        <div class="demo-users">
            <h4>حسابات تجريبية:</h4>
            <div class="demo-user" onclick="fillLogin('<EMAIL>', 'password')">
                👨‍💼 المدير: <EMAIL>
            </div>
            <div class="demo-user" onclick="fillLogin('<EMAIL>', 'password')">
                👤 مستخدم: <EMAIL>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001';
        
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loading = document.getElementById('loading');
            
            // Show loading
            loginBtn.disabled = true;
            loading.style.display = 'block';
            hideAlert();
            
            try {
                const response = await fetch(`${API_BASE}/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    // Save token
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('refreshToken', data.refresh_token);
                    localStorage.setItem('userId', data.user_id);
                    
                    showAlert('تم تسجيل الدخول بنجاح! جاري التوجيه...', 'success');
                    
                    // Redirect to admin dashboard after 2 seconds
                    setTimeout(() => {
                        window.location.href = 'admin_dashboard.html';
                    }, 2000);
                    
                } else {
                    showAlert('خطأ في تسجيل الدخول: ' + data.message, 'error');
                }
                
            } catch (error) {
                showAlert('خطأ في الاتصال: ' + error.message, 'error');
            } finally {
                loginBtn.disabled = false;
                loading.style.display = 'none';
            }
        });
        
        function fillLogin(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
        }
        
        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert alert-${type}`;
            alert.style.display = 'block';
        }
        
        function hideAlert() {
            document.getElementById('alert').style.display = 'none';
        }
        
        function showRegister() {
            window.location.href = 'register.html';
        }
        
        // Check if already logged in
        if (localStorage.getItem('authToken')) {
            showAlert('أنت مسجل دخول بالفعل، جاري التوجيه...', 'success');
            setTimeout(() => {
                window.location.href = 'admin_dashboard.html';
            }, 1000);
        }
    </script>
</body>
</html>
