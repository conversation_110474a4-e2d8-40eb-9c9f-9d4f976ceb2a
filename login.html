<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام المايكروسيرفس</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .title {
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
            direction: ltr;
            text-align: left;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .register-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .register-link:hover {
            text-decoration: underline;
        }

        .alert {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            display: none;
            margin: 10px 0;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .demo-users {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: right;
        }

        .demo-users h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .demo-user {
            background: white;
            padding: 8px;
            margin: 5px 0;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.2s;
            font-size: 14px;
        }

        .demo-user:hover {
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">🔐</div>
        <h1 class="title">تسجيل الدخول</h1>
        
        <div id="alert" class="alert" style="display: none;"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">البريد الإلكتروني:</label>
                <input type="email" id="email" name="email" required placeholder="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required placeholder="password">
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">
                دخول
            </button>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>جاري تسجيل الدخول...</p>
            </div>
        </form>
        
        <p>
            ليس لديك حساب؟ 
            <a href="#" class="register-link" onclick="showRegister()">إنشاء حساب جديد</a>
        </p>
        
        <div class="demo-users">
            <h4>حسابات تجريبية:</h4>
            <div class="demo-user" onclick="fillLogin('<EMAIL>', 'password')">
                👨‍💼 المدير: <EMAIL>
            </div>
            <div class="demo-user" onclick="fillLogin('<EMAIL>', 'password')">
                👤 مستخدم: <EMAIL>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001';
        
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loading = document.getElementById('loading');
            
            // Show loading
            loginBtn.disabled = true;
            loading.style.display = 'block';
            hideAlert();
            
            try {
                const response = await fetch(`${API_BASE}/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    // Save token
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('refreshToken', data.refresh_token);
                    localStorage.setItem('userId', data.user_id);
                    
                    showAlert('تم تسجيل الدخول بنجاح! جاري التوجيه...', 'success');
                    
                    // Redirect to dashboard after 2 seconds
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 2000);
                    
                } else {
                    showAlert('خطأ في تسجيل الدخول: ' + data.message, 'error');
                }
                
            } catch (error) {
                showAlert('خطأ في الاتصال: ' + error.message, 'error');
            } finally {
                loginBtn.disabled = false;
                loading.style.display = 'none';
            }
        });
        
        function fillLogin(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
        }
        
        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert alert-${type}`;
            alert.style.display = 'block';
        }
        
        function hideAlert() {
            document.getElementById('alert').style.display = 'none';
        }
        
        function showRegister() {
            window.location.href = 'register.html';
        }
        
        // Check if already logged in
        if (localStorage.getItem('authToken')) {
            showAlert('أنت مسجل دخول بالفعل، جاري التوجيه...', 'success');
            setTimeout(() => {
                window.location.href = 'dashboard.html';
            }, 1000);
        }
    </script>
</body>
</html>
