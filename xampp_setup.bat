@echo off
echo 🚀 Setting up Microservices for XAMPP...
echo.

echo 📋 Creating database and tables...
php -f setup_xampp.php

echo.
echo 🌐 Starting PHP built-in servers...

echo Starting Auth Service on port 8001...
start "Auth Service" php -S localhost:8001 -t auth-service

echo Starting User Service on port 8002...
start "User Service" php -S localhost:8002 -t user-service

echo Starting Product Service on port 8003...
start "Product Service" php -S localhost:8003 -t product-service

echo Starting Order Service on port 8004...
start "Order Service" php -S localhost:8004 -t order-service

echo.
echo ✅ Services are starting...
echo.
echo 📍 You can now access:
echo    - Auth Service: http://localhost:8001
echo    - User Service: http://localhost:8002
echo    - Product Service: http://localhost:8003
echo    - Order Service: http://localhost:8004
echo.
echo 🧪 Test the services:
echo    curl http://localhost:8001/health
echo    curl http://localhost:8002/health
echo    curl http://localhost:8003/health
echo    curl http://localhost:8004/health
echo.
echo Press any key to continue...
pause
