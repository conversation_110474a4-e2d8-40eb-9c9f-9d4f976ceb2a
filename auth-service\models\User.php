<?php
class User {
    private $conn;
    private $table = 'users';

    public function __construct($db) {
        $this->conn = $db;
    }

    public function create($name, $email, $password) {
        $query = 'INSERT INTO ' . $this->table . ' (name, email, password) VALUES (:name, :email, :password)';
        $stmt = $this->conn->prepare($query);
        
        return $stmt->execute([
            ':name' => $name,
            ':email' => $email,
            ':password' => $password
        ]);
    }

    public function login($email, $password) {
        $query = 'SELECT id, password FROM ' . $this->table . ' WHERE email = :email';
        $stmt = $this->conn->prepare($query);
        $stmt->execute([':email' => $email]);
        
        if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            if (password_verify($password, $row['password'])) {
                return $row;
            }
        }
        return false;
    }

    public function emailExists($email) {
        $query = 'SELECT id FROM ' . $this->table . ' WHERE email = :email';
        $stmt = $this->conn->prepare($query);
        $stmt->execute([':email' => $email]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ? true : false;
    }

    public function storeToken($userId, $token) {
        $query = 'UPDATE ' . $this->table . ' SET token = :token WHERE id = :id';
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([
            ':token' => $token,
            ':id' => $userId
        ]);
    }

    public function storeRefreshToken($userId, $refreshToken) {
        $query = 'UPDATE ' . $this->table . ' SET refresh_token = :refresh_token WHERE id = :id';
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([
            ':refresh_token' => $refreshToken,
            ':id' => $userId
        ]);
    }

    public function verifyRefreshToken($userId, $refreshToken) {
        $query = 'SELECT id FROM ' . $this->table . ' WHERE id = :id AND refresh_token = :refresh_token';
        $stmt = $this->conn->prepare($query);
        $stmt->execute([
            ':id' => $userId,
            ':refresh_token' => $refreshToken
        ]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ? true : false;
    }

    public function clearRefreshToken($userId) {
        $query = 'UPDATE ' . $this->table . ' SET refresh_token = NULL WHERE id = :id';
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([':id' => $userId]);
    }
}
