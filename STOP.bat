@echo off
title Microservices - Smart Stopper
color 0C

echo.
echo 🛑 Microservices Smart Stopper
echo ==============================
echo.

:: Check what's currently running
set DOCKER_RUNNING=0
set XAMPP_RUNNING=0

if exist ".running_docker" (
    set DOCKER_RUNNING=1
    echo 🐳 Docker services detected
)

if exist ".running_xampp" (
    set XAMPP_RUNNING=1
    echo 🔧 XAMPP services detected
)

if %DOCKER_RUNNING%==0 if %XAMPP_RUNNING%==0 (
    echo ℹ️ No running services detected
    echo.
    echo 💡 If services are running manually:
    echo   - For Docker: docker-compose down
    echo   - For XAMPP: Close service windows
    echo.
    pause
    exit /b 0
)

echo.
echo 🔍 Stopping detected services...
echo.

:: Stop Docker services
if %DOCKER_RUNNING%==1 (
    echo 🐳 Stopping Docker services...
    docker-compose down
    
    if %errorlevel% equ 0 (
        echo ✅ Docker services stopped
        del .running_docker >nul 2>&1
    ) else (
        echo ⚠️ Docker stop command failed (services may already be stopped)
        del .running_docker >nul 2>&1
    )
    echo.
)

:: Stop XAMPP services
if %XAMPP_RUNNING%==1 (
    echo 🔧 Stopping XAMPP services...
    
    :: Try to close service windows gracefully
    taskkill /f /im php.exe >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ PHP services stopped
    ) else (
        echo ℹ️ No PHP services found running
    )
    
    :: Clean up status file
    del .running_xampp >nul 2>&1
    echo ✅ XAMPP services stopped
    echo.
)

echo 🎉 All services stopped successfully!
echo.
echo 💡 To start again, run: RUN.bat
echo.
pause
