<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT');
header('Access-Control-Allow-Headers: Access-Control-Allow-Headers, Content-Type, Access-Control-Allow-Methods, Authorization, X-Requested-With');

require_once '../shared/middleware/AuthMiddleware.php';
require_once 'controllers/UserController.php';

$auth = new AuthMiddleware();
$userController = new UserController();

$method = $_SERVER['REQUEST_METHOD'];
$uri = $_SERVER['REQUEST_URI'];

// Health check endpoint
if ($method === 'GET' && $uri === '/health') {
    echo json_encode(['status' => 'healthy', 'service' => 'user-service']);
    exit();
}

// Require authentication
$currentUserId = $auth->requireAuth();

$userId = isset($_GET['user_id']) ? $_GET['user_id'] : null;

switch ($method) {
    case 'GET':
        if ($userId) {
            echo $userController->getProfile($userId);
        } else {
            $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
            echo $userController->listUsers($page, $limit);
        }
        break;

    case 'PUT':
        if ($userId) {
            $data = json_decode(file_get_contents("php://input"), true);
            echo $userController->updateProfile($userId, $data);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'User ID required']);
        }
        break;

    default:
        echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
}
