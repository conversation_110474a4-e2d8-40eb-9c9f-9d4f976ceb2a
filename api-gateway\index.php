<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Access-Control-Allow-Headers, Content-Type, Access-Control-Allow-Methods, Authorization, X-Requested-With');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config/ServiceRegistry.php';
require_once 'middleware/RateLimiter.php';
require_once 'middleware/LoadBalancer.php';

class APIGateway {
    private $serviceRegistry;
    private $rateLimiter;
    private $loadBalancer;
    
    public function __construct() {
        $this->serviceRegistry = new ServiceRegistry();
        $this->rateLimiter = new RateLimiter();
        $this->loadBalancer = new LoadBalancer();
    }
    
    public function route() {
        $uri = $_SERVER['REQUEST_URI'];
        $method = $_SERVER['REQUEST_METHOD'];
        
        // Remove query string
        $uri = strtok($uri, '?');
        
        // Health check for gateway
        if ($uri === '/health') {
            echo json_encode(['status' => 'healthy', 'service' => 'api-gateway']);
            return;
        }
        
        // Rate limiting
        $clientIp = $_SERVER['REMOTE_ADDR'];
        if (!$this->rateLimiter->isAllowed($clientIp)) {
            http_response_code(429);
            echo json_encode(['status' => 'error', 'message' => 'Rate limit exceeded']);
            return;
        }
        
        // Route to appropriate service
        $service = $this->getServiceFromUri($uri);
        
        if (!$service) {
            http_response_code(404);
            echo json_encode(['status' => 'error', 'message' => 'Service not found']);
            return;
        }
        
        // Get service URL with load balancing
        $serviceUrl = $this->loadBalancer->getServiceUrl($service);
        
        if (!$serviceUrl) {
            http_response_code(503);
            echo json_encode(['status' => 'error', 'message' => 'Service unavailable']);
            return;
        }
        
        // Forward request
        $this->forwardRequest($serviceUrl, $uri, $method);
    }
    
    private function getServiceFromUri($uri) {
        if (strpos($uri, '/auth') === 0) {
            return 'auth-service';
        } elseif (strpos($uri, '/users') === 0) {
            return 'user-service';
        } elseif (strpos($uri, '/products') === 0) {
            return 'product-service';
        } elseif (strpos($uri, '/orders') === 0) {
            return 'order-service';
        }
        
        return null;
    }
    
    private function forwardRequest($serviceUrl, $uri, $method) {
        $headers = getallheaders();
        $data = file_get_contents('php://input');
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $serviceUrl . $uri);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        // Forward headers
        $forwardHeaders = [];
        foreach ($headers as $key => $value) {
            if (strtolower($key) !== 'host') {
                $forwardHeaders[] = "$key: $value";
            }
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $forwardHeaders);
        
        // Forward body for POST/PUT requests
        if (in_array($method, ['POST', 'PUT', 'PATCH']) && $data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            http_response_code(502);
            echo json_encode(['status' => 'error', 'message' => 'Gateway error: ' . $error]);
            return;
        }
        
        http_response_code($httpCode);
        echo $response;
    }
}

// Initialize and route
$gateway = new APIGateway();
$gateway->route();
