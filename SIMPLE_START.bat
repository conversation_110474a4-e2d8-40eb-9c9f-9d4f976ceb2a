@echo off
echo 🚀 Starting Microservices...

"C:\xampp\php\php.exe" -S localhost:8001 -t auth-service &
"C:\xampp\php\php.exe" -S localhost:8002 -t user-service &
"C:\xampp\php\php.exe" -S localhost:8003 -t product-service &
"C:\xampp\php\php.exe" -S localhost:8004 -t order-service &

echo ✅ Services started!
echo 📍 URLs:
echo   http://localhost:8001 - Auth Service
echo   http://localhost:8002 - User Service  
echo   http://localhost:8003 - Product Service
echo   http://localhost:8004 - Order Service

pause
