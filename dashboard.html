<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة الشركة</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            direction: rtl;
        }

        .header {
            background: #ffffff;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .admin-toggle {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin-right: 15px;
        }

        .admin-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .admin-toggle.active {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            transform: rotate(180deg);
        }

        .admin-toggle.active:hover {
            transform: rotate(180deg) scale(1.1);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .company-name {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-info {
            text-align: right;
        }

        .user-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .user-role {
            font-size: 12px;
            color: #6c757d;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .logout-btn:hover {
            background: #c82333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .page-title {
            margin-bottom: 30px;
        }

        .page-title h1 {
            font-size: 28px;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .page-title p {
            color: #6c757d;
            font-size: 14px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 25px;
            transition: box-shadow 0.3s;
        }

        .stat-card:hover {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .stat-icon {
            width: 45px;
            height: 45px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .stat-icon.primary { background: #3498db; }
        .stat-icon.success { background: #27ae60; }
        .stat-icon.warning { background: #f39c12; }
        .stat-icon.danger { background: #e74c3c; }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            overflow: hidden;
        }

        .card-header {
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 20px 25px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-body {
            padding: 25px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .table td {
            color: #6c757d;
            font-size: 14px;
        }

        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .status.active {
            background: #d4edda;
            color: #155724;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
        }

        .status.inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .activity-item {
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-time {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .activity-text {
            font-size: 14px;
            color: #495057;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            right: -350px;
            width: 350px;
            height: 100vh;
            background: #ffffff;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
            transition: right 0.3s ease;
            z-index: 2000;
            border-left: 1px solid #e9ecef;
        }

        .sidebar.open {
            right: 0;
        }

        .sidebar-header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
        }

        .close-sidebar {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .close-sidebar:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar-content {
            padding: 20px;
            height: calc(100vh - 80px);
            overflow-y: auto;
        }

        .tool-category {
            margin-bottom: 30px;
        }

        .category-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e9ecef;
        }

        .tool-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin: 8px 0;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            color: #495057;
        }

        .tool-item:hover {
            background: #e9ecef;
            transform: translateX(-5px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .tool-icon {
            width: 40px;
            height: 40px;
            background: #3498db;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-size: 18px;
            color: white;
        }

        .tool-icon.success { background: #27ae60; }
        .tool-icon.warning { background: #f39c12; }
        .tool-icon.danger { background: #e74c3c; }
        .tool-icon.info { background: #17a2b8; }

        .tool-info h4 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #2c3e50;
        }

        .tool-info p {
            font-size: 12px;
            color: #6c757d;
            margin: 0;
        }

        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1500;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .tools-toggle-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .tools-toggle-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        /* Footer Styles */
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            margin-top: 50px;
            padding: 40px 0 20px;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 30px;
        }

        .footer-section h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #ecf0f1;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .footer-section p,
        .footer-section li {
            color: #bdc3c7;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section li {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 0;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .contact-item:last-child {
            border-bottom: none;
        }

        .contact-icon {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .social-links {
            display: flex;
            gap: 15px;
            margin-top: 15px;
        }

        .social-link {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 20px;
            text-align: center;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .footer-logo-icon {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .copyright {
            color: #95a5a6;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .footer-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .footer-bottom {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="white" stroke-width="2" stroke-linejoin="round"/>
                        <path d="M2 17L12 22L22 17" stroke="white" stroke-width="2" stroke-linejoin="round"/>
                        <path d="M2 12L12 17L22 12" stroke="white" stroke-width="2" stroke-linejoin="round"/>
                    </svg>
                </div>
                <!-- Admin Tools Toggle Button -->
                <button class="admin-toggle" id="adminToggle" onclick="toggleAdminTools()" title="أدوات الإدارة">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" id="toggleIcon">
                        <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
                <div class="company-name">نظام إدارة الشركة</div>
            </div>

            <div class="user-section">
                <div class="user-info">
                    <div class="user-name">المدير العام</div>
                    <div class="user-role">مدير النظام</div>
                </div>
                <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="page-title">
            <h1>لوحة التحكم الرئيسية</h1>
            <p>نظرة عامة على حالة النظام والعمليات</p>
        </div>

        <div id="alert-container"></div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon success">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="12" cy="7" r="4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
                <div class="stat-value" id="totalUsers">156</div>
                <div class="stat-label">إجمالي المستخدمين</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon primary">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M3 3V21H21" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M9 9L12 6L16 10L21 5" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
                <div class="stat-value" id="activeProjects">24</div>
                <div class="stat-label">المشاريع النشطة</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon warning">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M10.29 3.86L1.82 18C1.64 18.37 1.54 18.78 1.54 19.2C1.54 20.77 2.83 22.06 4.4 22.06H21.6C22.37 22.06 23.09 21.73 23.62 21.2C24.15 20.67 24.48 19.95 24.48 19.18C24.48 18.76 24.38 18.35 24.2 17.98L15.73 3.84C15.37 3.2 14.8 2.7 14.11 2.42C13.42 2.14 12.66 2.1 11.95 2.32C11.24 2.54 10.63 3.01 10.21 3.64C9.79 4.27 9.58 5.02 9.6 5.78L10.29 3.86Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M12 9V13" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M12 17H12.01" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
                <div class="stat-value" id="pendingTasks">8</div>
                <div class="stat-label">المهام المعلقة</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon success">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M22 11.08V12C21.9988 14.1564 21.3005 16.2547 20.0093 17.9818C18.7182 19.7088 16.9033 20.9725 14.8354 21.5839C12.7674 22.1953 10.5573 22.1219 8.53447 21.3746C6.51168 20.6273 4.78465 19.2461 3.61096 17.4371C2.43727 15.628 1.87979 13.4881 2.02168 11.3363C2.16356 9.18455 2.99721 7.13631 4.39828 5.49706C5.79935 3.85781 7.69279 2.71537 9.79619 2.24013C11.8996 1.76488 14.1003 1.98234 16.07 2.86" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M22 4L12 14.01L9 11.01" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
                <div class="stat-value">99.5%</div>
                <div class="stat-label">معدل الأداء</div>
            </div>
        </div>

        <!-- الرسوم البيانية -->
        <div class="card" style="margin-bottom: 30px;">
            <div class="card-header">
                <div class="card-title">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="margin-left: 8px;">
                        <path d="M3 3V21H21" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M9 9L12 6L16 10L21 5" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    الرسوم البيانية والتحليلات
                </div>
            </div>
            <div class="card-body">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 30px;">
                    <!-- رسم بياني للمستخدمين الجدد -->
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                        <h4 style="margin-bottom: 15px; color: #2c3e50;">المستخدمين الجدد (آخر 7 أيام)</h4>
                        <canvas id="newUsersChart" width="400" height="200"></canvas>
                    </div>

                    <!-- رسم بياني دائري للمشاريع -->
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                        <h4 style="margin-bottom: 15px; color: #2c3e50;">حالة المشاريع</h4>
                        <canvas id="projectsChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- رسم بياني للأداء الشهري -->
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-top: 30px;">
                    <h4 style="margin-bottom: 15px; color: #2c3e50;">الأداء الشهري (المهام المكتملة)</h4>
                    <canvas id="performanceChart" width="800" height="300"></canvas>
                </div>
            </div>
        </div>

        <div class="content-grid">
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="margin-left: 8px;">
                            <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M14 2V8H20" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M16 13H8" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M16 17H8" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M10 9H9H8" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        حالة الخدمات
                    </div>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الخدمة</th>
                                <th>الحالة</th>
                                <th>آخر فحص</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="servicesTable">
                            <!-- سيتم ملء البيانات من JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="margin-left: 8px;">
                            <path d="M12 8V12L16 16" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="12" cy="12" r="10" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        النشاط الأخير
                    </div>
                </div>
                <div class="card-body" id="activitiesContainer">
                    <!-- سيتم ملء البيانات من JavaScript -->
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <div class="card-title">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="margin-left: 8px;">
                        <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    الأداء والإنتاجية
                </div>
            </div>
            <div class="card-body">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px;">
                    <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; color: white;">
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 8px;">98.5%</div>
                        <div style="font-size: 14px; opacity: 0.9;">كفاءة النظام</div>
                    </div>
                    <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 12px; color: white;">
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 8px;">2.3s</div>
                        <div style="font-size: 14px; opacity: 0.9;">متوسط الاستجابة</div>
                    </div>
                    <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); border-radius: 12px; color: white;">
                        <div style="font-size: 32px; font-weight: bold; margin-bottom: 8px;">99.9%</div>
                        <div style="font-size: 14px; opacity: 0.9;">وقت التشغيل</div>
                    </div>
                </div>

                <div style="margin-top: 25px; padding: 20px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 12px; text-align: center; color: white;">
                    <div style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="white" stroke-width="2" stroke-linejoin="round"/>
                            <path d="M2 17L12 22L22 17" stroke="white" stroke-width="2" stroke-linejoin="round"/>
                            <path d="M2 12L12 17L22 12" stroke="white" stroke-width="2" stroke-linejoin="round"/>
                        </svg>
                        <div>
                            <div style="font-size: 18px; font-weight: 600; margin-bottom: 4px;">نظام إدارة متطور</div>
                            <div style="font-size: 14px; opacity: 0.9;">جميع الخدمات تعمل بكفاءة عالية</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <div class="footer-content">
            <div class="footer-grid">
                <!-- معلومات الشركة -->
                <div class="footer-section">
                    <h3>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        </svg>
                        نظام إدارة الشركة
                    </h3>
                    <p>منصة إدارية متكاملة تقدم حلول تقنية متطورة لإدارة الأعمال بكفاءة وأمان عالي. نحن نسعى لتقديم أفضل الخدمات التقنية لعملائنا.</p>
                    <div class="social-links">
                        <a href="#" class="social-link" title="فيسبوك">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link" title="تويتر">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link" title="لينكد إن">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link" title="إنستغرام">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- معلومات الاتصال -->
                <div class="footer-section">
                    <h3>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M22 16.92V7.08C21.9996 6.71003 21.9071 6.34687 21.731 6.02C21.5549 5.69312 21.3001 5.41345 20.99 5.2L13.99 0.68C13.6718 0.462477 13.2956 0.345703 12.91 0.345703C12.5244 0.345703 12.1482 0.462477 11.83 0.68L4.83 5.2C4.51994 5.41345 4.26513 5.69312 4.08902 6.02C3.91292 6.34687 3.82041 6.71003 3.82 7.08V16.92C3.82041 17.29 3.91292 17.6531 4.08902 17.98C4.26513 18.3069 4.51994 18.5865 4.83 18.8L11.83 23.32C12.1482 23.5375 12.5244 23.6543 12.91 23.6543C13.2956 23.6543 13.6718 23.5375 13.99 23.32L20.99 18.8C21.3001 18.5865 21.5549 18.3069 21.731 17.98C21.9071 17.6531 21.9996 17.29 22 16.92Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        معلومات الاتصال
                    </h3>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                                <path d="M21 10C21 17 12 23 12 23S3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.3639 3.63604C20.0518 5.32387 21 7.61305 21 10Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="10" r="3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div>
                            <strong>العنوان:</strong><br>
                            شارع الملك فهد، الرياض 12345<br>
                            المملكة العربية السعودية
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                                <path d="M22 6C22 4.9 21.1 4 20 4H4C2.9 4 2 4.9 2 6M22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6M22 6L12 13L2 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div>
                            <strong>البريد الإلكتروني:</strong><br>
                            <EMAIL><br>
                            <EMAIL>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                                <path d="M22 16.92V7.08C21.9996 6.71003 21.9071 6.34687 21.731 6.02C21.5549 5.69312 21.3001 5.41345 20.99 5.2L13.99 0.68C13.6718 0.462477 13.2956 0.345703 12.91 0.345703C12.5244 0.345703 12.1482 0.462477 11.83 0.68L4.83 5.2C4.51994 5.41345 4.26513 5.69312 4.08902 6.02C3.91292 6.34687 3.82041 6.71003 3.82 7.08V16.92C3.82041 17.29 3.91292 17.6531 4.08902 17.98C4.26513 18.3069 4.51994 18.5865 4.83 18.8L11.83 23.32C12.1482 23.5375 12.5244 23.6543 12.91 23.6543C13.2956 23.6543 13.6718 23.5375 13.99 23.32L20.99 18.8C21.3001 18.5865 21.5549 18.3069 21.731 17.98C21.9071 17.6531 21.9996 17.29 22 16.92Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M15.05 9C15.05 10.657 13.707 12 12.05 12C10.393 12 9.05 10.657 9.05 9C9.05 7.343 10.393 6 12.05 6C13.707 6 15.05 7.343 15.05 9Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M12.05 22V12" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div>
                            <strong>أرقام الهاتف:</strong><br>
                            +966 11 123 4567<br>
                            +966 50 987 6543
                        </div>
                    </div>
                </div>

                <!-- الخدمات -->
                <div class="footer-section">
                    <h3>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        </svg>
                        خدماتنا
                    </h3>
                    <ul>
                        <li>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M9 12L11 14L15 10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="12" r="10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            إدارة المستخدمين والصلاحيات
                        </li>
                        <li>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M9 12L11 14L15 10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="12" r="10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            أنظمة الأمان المتقدمة
                        </li>
                        <li>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M9 12L11 14L15 10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="12" r="10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            التقارير والإحصائيات
                        </li>
                        <li>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M9 12L11 14L15 10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="12" r="10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            النسخ الاحتياطية
                        </li>
                        <li>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M9 12L11 14L15 10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="12" r="10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            الدعم الفني 24/7
                        </li>
                    </ul>
                </div>

                <!-- روابط مهمة -->
                <div class="footer-section">
                    <h3>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M10 13C10.4295 13.5741 10.9774 14.0491 11.6066 14.3929C12.2357 14.7367 12.9315 14.9411 13.6467 14.9923C14.3618 15.0435 15.0796 14.9403 15.7513 14.6897C16.4231 14.4392 17.0331 14.047 17.54 13.54L20.54 10.54C21.4508 9.59695 21.9548 8.33394 21.9434 7.02296C21.932 5.71198 21.4061 4.45791 20.4791 3.53087C19.5521 2.60383 18.298 2.07799 16.987 2.0666C15.676 2.0552 14.413 2.55918 13.47 3.46997L11.75 5.17997" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M14 11C13.5705 10.4259 13.0226 9.95085 12.3934 9.60707C11.7643 9.26329 11.0685 9.05886 10.3533 9.00764C9.63819 8.95643 8.92037 9.05968 8.24864 9.31022C7.5769 9.56077 6.96687 9.95295 6.45996 10.46L3.45996 13.46C2.54917 14.403 2.04519 15.666 2.05659 16.977C2.06798 18.288 2.59382 19.5421 3.52086 20.4691C4.4479 21.3961 5.70197 21.922 7.01295 21.9334C8.32393 21.9448 9.58694 21.4408 10.53 20.53L12.24 18.82" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        روابط مهمة
                    </h3>
                    <ul>
                        <li>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M9 12L11 14L15 10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="12" r="10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            سياسة الخصوصية
                        </li>
                        <li>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M9 12L11 14L15 10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="12" r="10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            شروط الاستخدام
                        </li>
                        <li>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M9 12L11 14L15 10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="12" r="10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            الأسئلة الشائعة
                        </li>
                        <li>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M9 12L11 14L15 10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="12" r="10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            مركز المساعدة
                        </li>
                        <li>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M9 12L11 14L15 10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="12" r="10" stroke="#667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            اتصل بنا
                        </li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-logo">
                    <div class="footer-logo-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="white" stroke-width="2" stroke-linejoin="round"/>
                            <path d="M2 17L12 22L22 17" stroke="white" stroke-width="2" stroke-linejoin="round"/>
                            <path d="M2 12L12 17L22 12" stroke="white" stroke-width="2" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <span style="font-weight: 600;">نظام إدارة الشركة</span>
                </div>
                <div class="copyright">
                    © 2024 نظام إدارة الشركة. جميع الحقوق محفوظة.
                </div>
            </div>
        </div>
    </div>

        <!-- Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="closeSidebar()"></div>

        <!-- Sidebar -->
        <div class="sidebar" id="adminSidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">أدوات الإدارة</div>
                <button class="close-sidebar" onclick="closeSidebar()">×</button>
            </div>
            <div class="sidebar-content">

                <div class="tool-category">
                    <div class="category-title">إدارة المستخدمين</div>
                    <div class="tool-item" onclick="manageUsers()">
                        <div class="tool-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="7" r="4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="tool-info">
                            <h4>إدارة المستخدمين</h4>
                            <p>إضافة وتعديل وحذف المستخدمين</p>
                        </div>
                    </div>
                    <div class="tool-item" onclick="manageRoles()">
                        <div class="tool-icon info">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M9 12L11 14L15 10" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="tool-info">
                            <h4>إدارة الصلاحيات</h4>
                            <p>تحديد صلاحيات المستخدمين</p>
                        </div>
                    </div>
                </div>

                <div class="tool-category">
                    <div class="category-title">النظام والأمان</div>
                    <div class="tool-item" onclick="createBackup()">
                        <div class="tool-icon success">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M7 10L12 15L17 10" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M12 15V3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="tool-info">
                            <h4>نسخة احتياطية</h4>
                            <p>إنشاء نسخة احتياطية من البيانات</p>
                        </div>
                    </div>
                    <div class="tool-item" onclick="securityAudit()">
                        <div class="tool-icon warning">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M12 22S8 18 8 12V7L12 5L16 7V12C16 18 12 22 12 22Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="tool-info">
                            <h4>مراجعة الأمان</h4>
                            <p>فحص شامل لأمان النظام</p>
                        </div>
                    </div>
                    <div class="tool-item" onclick="systemSettings()">
                        <div class="tool-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="3" stroke="white" stroke-width="2"/>
                                <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2573 9.77251 19.9887C9.5799 19.7201 9.31074 19.5176 9 19.41C8.69838 19.2769 8.36381 19.2372 8.03941 19.296C7.71502 19.3548 7.41568 19.5095 7.18 19.74L7.12 19.8C6.93425 19.986 6.71368 20.1335 6.47088 20.2341C6.22808 20.3348 5.96783 20.3866 5.705 20.3866C5.44217 20.3866 5.18192 20.3348 4.93912 20.2341C4.69632 20.1335 4.47575 19.986 4.29 19.8C4.10405 19.6143 3.95653 19.3937 3.85588 19.1509C3.75523 18.9081 3.70343 18.6478 3.70343 18.385C3.70343 18.1222 3.75523 17.8619 3.85588 17.6191C3.95653 17.3763 4.10405 17.1557 4.29 16.97L4.35 16.91C4.58054 16.6743 4.73519 16.375 4.794 16.0506C4.85282 15.7262 4.81312 15.3916 4.68 15.09C4.55324 14.7942 4.34276 14.542 4.07447 14.3643C3.80618 14.1866 3.49179 14.0913 3.17 14.09H3C2.46957 14.09 1.96086 13.8793 1.58579 13.5042C1.21071 13.1291 1 12.6204 1 12.09C1 11.5596 1.21071 11.0509 1.58579 10.6758C1.96086 10.3007 2.46957 10.09 3 10.09H3.09C3.42099 10.0823 3.742 9.97512 4.01062 9.78251C4.27925 9.5899 4.48167 9.32074 4.59 9.01C4.72312 8.70838 4.76282 8.37381 4.704 8.04941C4.64519 7.72502 4.49054 7.42568 4.26 7.19L4.2 7.13C4.01405 6.94425 3.86653 6.72368 3.76588 6.48088C3.66523 6.23808 3.61343 5.97783 3.61343 5.715C3.61343 5.45217 3.66523 5.19192 3.76588 4.94912C3.86653 4.70632 4.01405 4.48575 4.2 4.3C4.38575 4.11405 4.60632 3.96653 4.84912 3.86588C5.09192 3.76523 5.35217 3.71343 5.615 3.71343C5.87783 3.71343 6.13808 3.76523 6.38088 3.86588C6.62368 3.96653 6.84425 4.11405 7.03 4.3L7.09 4.36C7.32568 4.59054 7.62502 4.74519 7.94941 4.804C8.27381 4.86282 8.60838 4.82312 8.91 4.69H9C9.29577 4.56324 9.54802 4.35276 9.72569 4.08447C9.90337 3.81618 9.99872 3.50179 10 3.18V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="tool-info">
                            <h4>إعدادات النظام</h4>
                            <p>تكوين إعدادات النظام العامة</p>
                        </div>
                    </div>
                </div>

                <div class="tool-category">
                    <div class="category-title">التقارير والإحصائيات</div>
                    <div class="tool-item" onclick="viewReports()">
                        <div class="tool-icon info">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M3 3V21H21" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M9 9L12 6L16 10L21 5" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="tool-info">
                            <h4>عرض التقارير</h4>
                            <p>تقارير مفصلة عن أداء النظام</p>
                        </div>
                    </div>
                    <div class="tool-item" onclick="generateReport()">
                        <div class="tool-icon success">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M14 2V8H20" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M16 13H8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M16 17H8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M10 9H9H8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="tool-info">
                            <h4>إنشاء تقرير</h4>
                            <p>إنشاء تقرير مخصص</p>
                        </div>
                    </div>
                </div>

                <div class="tool-category">
                    <div class="category-title">الطوارئ</div>
                    <div class="tool-item" onclick="emergencyMode()">
                        <div class="tool-icon danger">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M10.29 3.86L1.82 18C1.64 18.37 1.54 18.78 1.54 19.2C1.54 20.77 2.83 22.06 4.4 22.06H21.6C22.37 22.06 23.09 21.73 23.62 21.2C24.15 20.67 24.48 19.95 24.48 19.18C24.48 18.76 24.38 18.35 24.2 17.98L15.73 3.84C15.37 3.2 14.8 2.7 14.11 2.42C13.42 2.14 12.66 2.1 11.95 2.32C11.24 2.54 10.63 3.01 10.21 3.64C9.79 4.27 9.58 5.02 9.6 5.78L10.29 3.86Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M12 9V13" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M12 17H12.01" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="tool-info">
                            <h4>وضع الطوارئ</h4>
                            <p>تفعيل وضع الطوارئ للنظام</p>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthentication();
            loadDashboardData();
            showWelcomeMessage();
        });

        // جلب البيانات من قاعدة البيانات
        async function loadDashboardData() {
            try {
                const response = await fetch('api/dashboard_data.php');
                const data = await response.json();

                if (data.success) {
                    updateStats(data.stats);
                    updateServicesTable(data.services);
                    updateActivities(data.activities);
                    createCharts(data.charts);
                }
            } catch (error) {
                console.error('خطأ في جلب البيانات:', error);
                showAlert('خطأ في جلب البيانات من الخادم', 'danger');
            }
        }

        // تحديث الإحصائيات
        function updateStats(stats) {
            document.getElementById('totalUsers').textContent = stats.total_users;
            document.getElementById('activeProjects').textContent = stats.active_projects;
            document.getElementById('pendingTasks').textContent = stats.pending_tasks;

            // تحديث معدل الأداء
            const performanceElement = document.querySelector('.stat-value:last-child');
            if (performanceElement) {
                performanceElement.textContent = stats.performance + '%';
            }
        }

        // تحديث جدول الخدمات
        function updateServicesTable(services) {
            const tbody = document.getElementById('servicesTable');
            tbody.innerHTML = '';

            services.forEach(service => {
                const statusClass = service.status === 'active' ? 'active' :
                                  service.status === 'warning' ? 'warning' : 'inactive';
                const statusText = service.status === 'active' ? 'نشط' :
                                 service.status === 'warning' ? 'بطيء' : 'متوقف';
                const buttonClass = service.status === 'active' ? 'btn-primary' :
                                  service.status === 'warning' ? 'btn-warning' : 'btn-danger';
                const buttonText = service.status === 'active' ? 'فحص' :
                                 service.status === 'warning' ? 'تحسين' : 'إصلاح';

                const timeAgo = getTimeAgo(service.last_check);

                const row = `
                    <tr>
                        <td>${service.service_name}</td>
                        <td><span class="status ${statusClass}">${statusText}</span></td>
                        <td>${timeAgo}</td>
                        <td><button class="btn ${buttonClass}">${buttonText}</button></td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        // تحديث النشاطات
        function updateActivities(activities) {
            const container = document.getElementById('activitiesContainer');
            container.innerHTML = '';

            activities.forEach(activity => {
                const timeAgo = getTimeAgo(activity.created_at);
                const activityItem = `
                    <div class="activity-item">
                        <div class="activity-time">${timeAgo}</div>
                        <div class="activity-text">${activity.action} - ${activity.user_name}</div>
                    </div>
                `;
                container.innerHTML += activityItem;
            });
        }

        // حساب الوقت المنقضي
        function getTimeAgo(dateString) {
            const now = new Date();
            const date = new Date(dateString);
            const diffInMinutes = Math.floor((now - date) / (1000 * 60));

            if (diffInMinutes < 1) return 'منذ ثوانٍ';
            if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;

            const diffInHours = Math.floor(diffInMinutes / 60);
            if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;

            const diffInDays = Math.floor(diffInHours / 24);
            return `منذ ${diffInDays} يوم`;
        }

        // إنشاء الرسوم البيانية
        function createCharts(chartsData) {
            createNewUsersChart(chartsData.new_users);
            createProjectsChart(chartsData.projects_status);
            createPerformanceChart(chartsData.monthly_performance);
        }

        // رسم بياني للمستخدمين الجدد
        function createNewUsersChart(data) {
            const ctx = document.getElementById('newUsersChart').getContext('2d');

            const labels = data.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' });
            });

            const values = data.map(item => item.count);

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'المستخدمين الجدد',
                        data: values,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        }

        // رسم بياني دائري للمشاريع
        function createProjectsChart(data) {
            const ctx = document.getElementById('projectsChart').getContext('2d');

            const labels = data.map(item => {
                const statusMap = {
                    'active': 'نشط',
                    'completed': 'مكتمل',
                    'pending': 'معلق',
                    'cancelled': 'ملغي'
                };
                return statusMap[item.status] || item.status;
            });

            const values = data.map(item => item.count);
            const colors = ['#27ae60', '#3498db', '#f39c12', '#e74c3c'];

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: colors,
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // رسم بياني للأداء الشهري
        function createPerformanceChart(data) {
            const ctx = document.getElementById('performanceChart').getContext('2d');

            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];

            const labels = data.map(item => monthNames[item.month - 1]);
            const values = data.map(item => item.tasks_completed);

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'المهام المكتملة',
                        data: values,
                        backgroundColor: 'rgba(102, 126, 234, 0.8)',
                        borderColor: '#667eea',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // تحديث البيانات كل 30 ثانية
        function startRealTimeUpdates() {
            setInterval(loadDashboardData, 30000);
        }

        function checkAuthentication() {
            const token = localStorage.getItem('authToken');
            if (!token) {
                window.location.href = 'login.html';
                return;
            }
        }

        function showWelcomeMessage() {
            showAlert('مرحباً بك في نظام إدارة الشركة. جميع الأنظمة تعمل بشكل طبيعي.', 'success');
            startRealTimeUpdates();
        }



        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alert-container');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.innerHTML = message;
            
            alertContainer.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        function manageUsers() {
            showAlert('تم فتح صفحة إدارة المستخدمين', 'success');
        }

        function createBackup() {
            showAlert('جاري إنشاء النسخة الاحتياطية...', 'warning');
            setTimeout(() => {
                showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            }, 3000);
        }

        function viewReports() {
            showAlert('تم فتح صفحة التقارير', 'success');
        }

        function systemSettings() {
            showAlert('تم فتح إعدادات النظام', 'success');
        }

        function securityAudit() {
            showAlert('جاري تشغيل مراجعة الأمان...', 'warning');
            setTimeout(() => {
                showAlert('تمت مراجعة الأمان بنجاح. لم يتم العثور على مشاكل', 'success');
            }, 4000);
        }

        function emergencyMode() {
            if (confirm('هل أنت متأكد من تفعيل وضع الطوارئ؟')) {
                showAlert('تم تفعيل وضع الطوارئ. تم إيقاف جميع العمليات غير الضرورية.', 'danger');
            }
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('authToken');
                localStorage.removeItem('refreshToken');
                localStorage.removeItem('userId');
                window.location.href = 'login.html';
            }
        }

        // Admin Tools Toggle Functions
        function toggleAdminTools() {
            const sidebar = document.getElementById('adminSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            const toggleBtn = document.getElementById('adminToggle');
            const toggleIcon = document.getElementById('toggleIcon');

            if (sidebar.classList.contains('open')) {
                // Close sidebar
                sidebar.classList.remove('open');
                overlay.classList.remove('show');
                toggleBtn.classList.remove('active');

                // Change icon back to down arrow
                toggleIcon.innerHTML = '<path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>';
            } else {
                // Open sidebar
                sidebar.classList.add('open');
                overlay.classList.add('show');
                toggleBtn.classList.add('active');

                // Change icon to up arrow
                toggleIcon.innerHTML = '<path d="M18 15L12 9L6 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>';
            }
        }

        // Legacy function for compatibility
        function toggleSidebar() {
            toggleAdminTools();
        }

        function closeSidebar() {
            const sidebar = document.getElementById('adminSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            const toggleBtn = document.getElementById('adminToggle');
            const toggleIcon = document.getElementById('toggleIcon');

            sidebar.classList.remove('open');
            overlay.classList.remove('show');
            toggleBtn.classList.remove('active');

            // Change icon back to down arrow
            toggleIcon.innerHTML = '<path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>';
        }

        // Close sidebar with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeSidebar();
            }
        });

        // Additional tool functions
        function manageRoles() {
            closeSidebar();
            showAlert('تم فتح صفحة إدارة الصلاحيات', 'success');
        }

        function generateReport() {
            closeSidebar();
            showAlert('جاري إنشاء التقرير المخصص...', 'warning');
            setTimeout(() => {
                showAlert('تم إنشاء التقرير بنجاح', 'success');
            }, 3000);
        }

        // Update existing functions to close sidebar
        const originalManageUsers = manageUsers;
        manageUsers = function() {
            closeSidebar();
            originalManageUsers();
        };

        const originalCreateBackup = createBackup;
        createBackup = function() {
            closeSidebar();
            originalCreateBackup();
        };

        const originalViewReports = viewReports;
        viewReports = function() {
            closeSidebar();
            originalViewReports();
        };

        const originalSystemSettings = systemSettings;
        systemSettings = function() {
            closeSidebar();
            originalSystemSettings();
        };

        const originalSecurityAudit = securityAudit;
        securityAudit = function() {
            closeSidebar();
            originalSecurityAudit();
        };

        const originalEmergencyMode = emergencyMode;
        emergencyMode = function() {
            closeSidebar();
            originalEmergencyMode();
        };
    </script>
</body>
</html>
