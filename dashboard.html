<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة الشركة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            direction: rtl;
        }

        .header {
            background: #ffffff;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .admin-toggle {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            z-index: 1001;
        }

        .admin-toggle:hover {
            transform: translate(-50%, -50%) scale(1.1);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .admin-toggle.active {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            transform: translate(-50%, -50%) rotate(180deg);
        }

        .admin-toggle.active:hover {
            transform: translate(-50%, -50%) rotate(180deg) scale(1.1);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .company-name {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-info {
            text-align: right;
        }

        .user-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .user-role {
            font-size: 12px;
            color: #6c757d;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .logout-btn:hover {
            background: #c82333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .page-title {
            margin-bottom: 30px;
        }

        .page-title h1 {
            font-size: 28px;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .page-title p {
            color: #6c757d;
            font-size: 14px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 25px;
            transition: box-shadow 0.3s;
        }

        .stat-card:hover {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .stat-icon {
            width: 45px;
            height: 45px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .stat-icon.primary { background: #3498db; }
        .stat-icon.success { background: #27ae60; }
        .stat-icon.warning { background: #f39c12; }
        .stat-icon.danger { background: #e74c3c; }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            overflow: hidden;
        }

        .card-header {
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 20px 25px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-body {
            padding: 25px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .table td {
            color: #6c757d;
            font-size: 14px;
        }

        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .status.active {
            background: #d4edda;
            color: #155724;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
        }

        .status.inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .activity-item {
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-time {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .activity-text {
            font-size: 14px;
            color: #495057;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            right: -350px;
            width: 350px;
            height: 100vh;
            background: #ffffff;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
            transition: right 0.3s ease;
            z-index: 2000;
            border-left: 1px solid #e9ecef;
        }

        .sidebar.open {
            right: 0;
        }

        .sidebar-header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
        }

        .close-sidebar {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .close-sidebar:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar-content {
            padding: 20px;
            height: calc(100vh - 80px);
            overflow-y: auto;
        }

        .tool-category {
            margin-bottom: 30px;
        }

        .category-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e9ecef;
        }

        .tool-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin: 8px 0;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            color: #495057;
        }

        .tool-item:hover {
            background: #e9ecef;
            transform: translateX(-5px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .tool-icon {
            width: 40px;
            height: 40px;
            background: #3498db;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-size: 18px;
            color: white;
        }

        .tool-icon.success { background: #27ae60; }
        .tool-icon.warning { background: #f39c12; }
        .tool-icon.danger { background: #e74c3c; }
        .tool-icon.info { background: #17a2b8; }

        .tool-info h4 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #2c3e50;
        }

        .tool-info p {
            font-size: 12px;
            color: #6c757d;
            margin: 0;
        }

        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1500;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .tools-toggle-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .tools-toggle-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="white" stroke-width="2" stroke-linejoin="round"/>
                        <path d="M2 17L12 22L22 17" stroke="white" stroke-width="2" stroke-linejoin="round"/>
                        <path d="M2 12L12 17L22 12" stroke="white" stroke-width="2" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="company-name">نظام إدارة الشركة</div>
            </div>

            <!-- Admin Tools Toggle Button -->
            <button class="admin-toggle" id="adminToggle" onclick="toggleAdminTools()" title="أدوات الإدارة">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" id="toggleIcon">
                    <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>

            <div class="user-section">
                <div class="user-info">
                    <div class="user-name">المدير العام</div>
                    <div class="user-role">مدير النظام</div>
                </div>
                <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="page-title">
            <h1>لوحة التحكم الرئيسية</h1>
            <p>نظرة عامة على حالة النظام والعمليات</p>
        </div>

        <div id="alert-container"></div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon success">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="12" cy="7" r="4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
                <div class="stat-value" id="totalUsers">156</div>
                <div class="stat-label">إجمالي المستخدمين</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon primary">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M3 3V21H21" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M9 9L12 6L16 10L21 5" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
                <div class="stat-value" id="activeProjects">24</div>
                <div class="stat-label">المشاريع النشطة</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon warning">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M10.29 3.86L1.82 18C1.64 18.37 1.54 18.78 1.54 19.2C1.54 20.77 2.83 22.06 4.4 22.06H21.6C22.37 22.06 23.09 21.73 23.62 21.2C24.15 20.67 24.48 19.95 24.48 19.18C24.48 18.76 24.38 18.35 24.2 17.98L15.73 3.84C15.37 3.2 14.8 2.7 14.11 2.42C13.42 2.14 12.66 2.1 11.95 2.32C11.24 2.54 10.63 3.01 10.21 3.64C9.79 4.27 9.58 5.02 9.6 5.78L10.29 3.86Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M12 9V13" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M12 17H12.01" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
                <div class="stat-value" id="pendingTasks">8</div>
                <div class="stat-label">المهام المعلقة</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon success">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M22 11.08V12C21.9988 14.1564 21.3005 16.2547 20.0093 17.9818C18.7182 19.7088 16.9033 20.9725 14.8354 21.5839C12.7674 22.1953 10.5573 22.1219 8.53447 21.3746C6.51168 20.6273 4.78465 19.2461 3.61096 17.4371C2.43727 15.628 1.87979 13.4881 2.02168 11.3363C2.16356 9.18455 2.99721 7.13631 4.39828 5.49706C5.79935 3.85781 7.69279 2.71537 9.79619 2.24013C11.8996 1.76488 14.1003 1.98234 16.07 2.86" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M22 4L12 14.01L9 11.01" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
                <div class="stat-value">99.5%</div>
                <div class="stat-label">معدل الأداء</div>
            </div>
        </div>

        <div class="content-grid">
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="margin-left: 8px;">
                            <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M14 2V8H20" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M16 13H8" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M16 17H8" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M10 9H9H8" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        حالة الخدمات
                    </div>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الخدمة</th>
                                <th>الحالة</th>
                                <th>آخر فحص</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>خدمة المصادقة</td>
                                <td><span class="status active">نشط</span></td>
                                <td>منذ دقيقة</td>
                                <td><button class="btn btn-primary">فحص</button></td>
                            </tr>
                            <tr>
                                <td>خدمة المستخدمين</td>
                                <td><span class="status active">نشط</span></td>
                                <td>منذ دقيقة</td>
                                <td><button class="btn btn-primary">فحص</button></td>
                            </tr>
                            <tr>
                                <td>قاعدة البيانات</td>
                                <td><span class="status warning">بطيء</span></td>
                                <td>منذ 3 دقائق</td>
                                <td><button class="btn btn-warning">تحسين</button></td>
                            </tr>
                            <tr>
                                <td>خدمة النسخ الاحتياطي</td>
                                <td><span class="status active">نشط</span></td>
                                <td>منذ 30 ثانية</td>
                                <td><button class="btn btn-primary">فحص</button></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="margin-left: 8px;">
                            <path d="M12 8V12L16 16" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="12" cy="12" r="10" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        النشاط الأخير
                    </div>
                </div>
                <div class="card-body">
                    <div class="activity-item">
                        <div class="activity-time">منذ 5 دقائق</div>
                        <div class="activity-text">تسجيل دخول مستخدم جديد</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-time">منذ 15 دقيقة</div>
                        <div class="activity-text">تحديث بيانات المشروع #123</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-time">منذ 30 دقيقة</div>
                        <div class="activity-text">إنشاء نسخة احتياطية</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-time">منذ ساعة</div>
                        <div class="activity-text">إضافة مستخدم جديد</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-time">منذ ساعتين</div>
                        <div class="activity-text">تحديث إعدادات النظام</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <div class="card-title">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="margin-left: 8px;">
                        <path d="M14.7 6.3C15.1 5.9 15.1 5.3 14.7 4.9C14.3 4.5 13.7 4.5 13.3 4.9L12 6.2L10.7 4.9C10.3 4.5 9.7 4.5 9.3 4.9C8.9 5.3 8.9 5.9 9.3 6.3L10.6 7.6L9.3 8.9C8.9 9.3 8.9 9.9 9.3 10.3C9.7 10.7 10.3 10.7 10.7 10.3L12 9L13.3 10.3C13.7 10.7 14.3 10.7 14.7 10.3C15.1 9.9 15.1 9.3 14.7 8.9L13.4 7.6L14.7 6.3Z" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M8 12H16" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M12 8V16" stroke="#495057" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    أدوات الإدارة
                </div>
            </div>
            <div class="card-body">
                <p style="color: #6c757d; margin-bottom: 20px;">
                    استخدم السهم في أعلى الصفحة للوصول إلى جميع أدوات الإدارة والتحكم في النظام
                </p>
                <div style="text-align: center; padding: 20px;">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" style="color: #6c757d;">
                        <path d="M12 5V19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M5 12L12 5L19 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <p style="color: #6c757d; margin-top: 10px; font-size: 14px;">
                        اضغط على السهم في الأعلى
                    </p>
                </div>
            </div>
        </div>

        <!-- Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="closeSidebar()"></div>

        <!-- Sidebar -->
        <div class="sidebar" id="adminSidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">أدوات الإدارة</div>
                <button class="close-sidebar" onclick="closeSidebar()">×</button>
            </div>
            <div class="sidebar-content">

                <div class="tool-category">
                    <div class="category-title">إدارة المستخدمين</div>
                    <div class="tool-item" onclick="manageUsers()">
                        <div class="tool-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="7" r="4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="tool-info">
                            <h4>إدارة المستخدمين</h4>
                            <p>إضافة وتعديل وحذف المستخدمين</p>
                        </div>
                    </div>
                    <div class="tool-item" onclick="manageRoles()">
                        <div class="tool-icon info">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M9 12L11 14L15 10" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="tool-info">
                            <h4>إدارة الصلاحيات</h4>
                            <p>تحديد صلاحيات المستخدمين</p>
                        </div>
                    </div>
                </div>

                <div class="tool-category">
                    <div class="category-title">النظام والأمان</div>
                    <div class="tool-item" onclick="createBackup()">
                        <div class="tool-icon success">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M7 10L12 15L17 10" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M12 15V3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="tool-info">
                            <h4>نسخة احتياطية</h4>
                            <p>إنشاء نسخة احتياطية من البيانات</p>
                        </div>
                    </div>
                    <div class="tool-item" onclick="securityAudit()">
                        <div class="tool-icon warning">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M12 22S8 18 8 12V7L12 5L16 7V12C16 18 12 22 12 22Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="tool-info">
                            <h4>مراجعة الأمان</h4>
                            <p>فحص شامل لأمان النظام</p>
                        </div>
                    </div>
                    <div class="tool-item" onclick="systemSettings()">
                        <div class="tool-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="3" stroke="white" stroke-width="2"/>
                                <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2573 9.77251 19.9887C9.5799 19.7201 9.31074 19.5176 9 19.41C8.69838 19.2769 8.36381 19.2372 8.03941 19.296C7.71502 19.3548 7.41568 19.5095 7.18 19.74L7.12 19.8C6.93425 19.986 6.71368 20.1335 6.47088 20.2341C6.22808 20.3348 5.96783 20.3866 5.705 20.3866C5.44217 20.3866 5.18192 20.3348 4.93912 20.2341C4.69632 20.1335 4.47575 19.986 4.29 19.8C4.10405 19.6143 3.95653 19.3937 3.85588 19.1509C3.75523 18.9081 3.70343 18.6478 3.70343 18.385C3.70343 18.1222 3.75523 17.8619 3.85588 17.6191C3.95653 17.3763 4.10405 17.1557 4.29 16.97L4.35 16.91C4.58054 16.6743 4.73519 16.375 4.794 16.0506C4.85282 15.7262 4.81312 15.3916 4.68 15.09C4.55324 14.7942 4.34276 14.542 4.07447 14.3643C3.80618 14.1866 3.49179 14.0913 3.17 14.09H3C2.46957 14.09 1.96086 13.8793 1.58579 13.5042C1.21071 13.1291 1 12.6204 1 12.09C1 11.5596 1.21071 11.0509 1.58579 10.6758C1.96086 10.3007 2.46957 10.09 3 10.09H3.09C3.42099 10.0823 3.742 9.97512 4.01062 9.78251C4.27925 9.5899 4.48167 9.32074 4.59 9.01C4.72312 8.70838 4.76282 8.37381 4.704 8.04941C4.64519 7.72502 4.49054 7.42568 4.26 7.19L4.2 7.13C4.01405 6.94425 3.86653 6.72368 3.76588 6.48088C3.66523 6.23808 3.61343 5.97783 3.61343 5.715C3.61343 5.45217 3.66523 5.19192 3.76588 4.94912C3.86653 4.70632 4.01405 4.48575 4.2 4.3C4.38575 4.11405 4.60632 3.96653 4.84912 3.86588C5.09192 3.76523 5.35217 3.71343 5.615 3.71343C5.87783 3.71343 6.13808 3.76523 6.38088 3.86588C6.62368 3.96653 6.84425 4.11405 7.03 4.3L7.09 4.36C7.32568 4.59054 7.62502 4.74519 7.94941 4.804C8.27381 4.86282 8.60838 4.82312 8.91 4.69H9C9.29577 4.56324 9.54802 4.35276 9.72569 4.08447C9.90337 3.81618 9.99872 3.50179 10 3.18V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="tool-info">
                            <h4>إعدادات النظام</h4>
                            <p>تكوين إعدادات النظام العامة</p>
                        </div>
                    </div>
                </div>

                <div class="tool-category">
                    <div class="category-title">التقارير والإحصائيات</div>
                    <div class="tool-item" onclick="viewReports()">
                        <div class="tool-icon info">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M3 3V21H21" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M9 9L12 6L16 10L21 5" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="tool-info">
                            <h4>عرض التقارير</h4>
                            <p>تقارير مفصلة عن أداء النظام</p>
                        </div>
                    </div>
                    <div class="tool-item" onclick="generateReport()">
                        <div class="tool-icon success">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M14 2V8H20" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M16 13H8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M16 17H8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M10 9H9H8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="tool-info">
                            <h4>إنشاء تقرير</h4>
                            <p>إنشاء تقرير مخصص</p>
                        </div>
                    </div>
                </div>

                <div class="tool-category">
                    <div class="category-title">الطوارئ</div>
                    <div class="tool-item" onclick="emergencyMode()">
                        <div class="tool-icon danger">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M10.29 3.86L1.82 18C1.64 18.37 1.54 18.78 1.54 19.2C1.54 20.77 2.83 22.06 4.4 22.06H21.6C22.37 22.06 23.09 21.73 23.62 21.2C24.15 20.67 24.48 19.95 24.48 19.18C24.48 18.76 24.38 18.35 24.2 17.98L15.73 3.84C15.37 3.2 14.8 2.7 14.11 2.42C13.42 2.14 12.66 2.1 11.95 2.32C11.24 2.54 10.63 3.01 10.21 3.64C9.79 4.27 9.58 5.02 9.6 5.78L10.29 3.86Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M12 9V13" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M12 17H12.01" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="tool-info">
                            <h4>وضع الطوارئ</h4>
                            <p>تفعيل وضع الطوارئ للنظام</p>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthentication();
            startRealTimeUpdates();
            showWelcomeMessage();
        });

        function checkAuthentication() {
            const token = localStorage.getItem('authToken');
            if (!token) {
                window.location.href = 'login.html';
                return;
            }
        }

        function showWelcomeMessage() {
            showAlert('مرحباً بك في نظام إدارة الشركة. جميع الأنظمة تعمل بشكل طبيعي.', 'success');
        }

        function startRealTimeUpdates() {
            setInterval(updateStats, 30000);
        }

        function updateStats() {
            document.getElementById('totalUsers').textContent = Math.floor(Math.random() * 50) + 150;
            document.getElementById('activeProjects').textContent = Math.floor(Math.random() * 10) + 20;
            document.getElementById('pendingTasks').textContent = Math.floor(Math.random() * 15) + 5;
        }

        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alert-container');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.innerHTML = message;
            
            alertContainer.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        function manageUsers() {
            showAlert('تم فتح صفحة إدارة المستخدمين', 'success');
        }

        function createBackup() {
            showAlert('جاري إنشاء النسخة الاحتياطية...', 'warning');
            setTimeout(() => {
                showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            }, 3000);
        }

        function viewReports() {
            showAlert('تم فتح صفحة التقارير', 'success');
        }

        function systemSettings() {
            showAlert('تم فتح إعدادات النظام', 'success');
        }

        function securityAudit() {
            showAlert('جاري تشغيل مراجعة الأمان...', 'warning');
            setTimeout(() => {
                showAlert('تمت مراجعة الأمان بنجاح. لم يتم العثور على مشاكل', 'success');
            }, 4000);
        }

        function emergencyMode() {
            if (confirm('هل أنت متأكد من تفعيل وضع الطوارئ؟')) {
                showAlert('تم تفعيل وضع الطوارئ. تم إيقاف جميع العمليات غير الضرورية.', 'danger');
            }
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('authToken');
                localStorage.removeItem('refreshToken');
                localStorage.removeItem('userId');
                window.location.href = 'login.html';
            }
        }

        // Admin Tools Toggle Functions
        function toggleAdminTools() {
            const sidebar = document.getElementById('adminSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            const toggleBtn = document.getElementById('adminToggle');
            const toggleIcon = document.getElementById('toggleIcon');

            if (sidebar.classList.contains('open')) {
                // Close sidebar
                sidebar.classList.remove('open');
                overlay.classList.remove('show');
                toggleBtn.classList.remove('active');

                // Change icon back to down arrow
                toggleIcon.innerHTML = '<path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>';
            } else {
                // Open sidebar
                sidebar.classList.add('open');
                overlay.classList.add('show');
                toggleBtn.classList.add('active');

                // Change icon to up arrow
                toggleIcon.innerHTML = '<path d="M18 15L12 9L6 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>';
            }
        }

        // Legacy function for compatibility
        function toggleSidebar() {
            toggleAdminTools();
        }

        function closeSidebar() {
            const sidebar = document.getElementById('adminSidebar');
            const overlay = document.getElementById('sidebarOverlay');
            const toggleBtn = document.getElementById('adminToggle');
            const toggleIcon = document.getElementById('toggleIcon');

            sidebar.classList.remove('open');
            overlay.classList.remove('show');
            toggleBtn.classList.remove('active');

            // Change icon back to down arrow
            toggleIcon.innerHTML = '<path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>';
        }

        // Close sidebar with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeSidebar();
            }
        });

        // Additional tool functions
        function manageRoles() {
            closeSidebar();
            showAlert('تم فتح صفحة إدارة الصلاحيات', 'success');
        }

        function generateReport() {
            closeSidebar();
            showAlert('جاري إنشاء التقرير المخصص...', 'warning');
            setTimeout(() => {
                showAlert('تم إنشاء التقرير بنجاح', 'success');
            }, 3000);
        }

        // Update existing functions to close sidebar
        const originalManageUsers = manageUsers;
        manageUsers = function() {
            closeSidebar();
            originalManageUsers();
        };

        const originalCreateBackup = createBackup;
        createBackup = function() {
            closeSidebar();
            originalCreateBackup();
        };

        const originalViewReports = viewReports;
        viewReports = function() {
            closeSidebar();
            originalViewReports();
        };

        const originalSystemSettings = systemSettings;
        systemSettings = function() {
            closeSidebar();
            originalSystemSettings();
        };

        const originalSecurityAudit = securityAudit;
        securityAudit = function() {
            closeSidebar();
            originalSecurityAudit();
        };

        const originalEmergencyMode = emergencyMode;
        emergencyMode = function() {
            closeSidebar();
            originalEmergencyMode();
        };
    </script>
</body>
</html>
