<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام المايكروسيرفس</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            direction: rtl;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .card h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #5a6fd8;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
        }

        .product-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .product-card h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .product-price {
            color: #667eea;
            font-weight: bold;
            font-size: 18px;
        }

        .product-stock {
            color: #28a745;
            font-size: 14px;
        }

        .loading {
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .service-status {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }

        .service-status.healthy {
            border-left-color: #28a745;
        }

        .service-status.error {
            border-left-color: #dc3545;
        }

        #activityContainer p {
            margin: 10px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏠 لوحة التحكم</h1>
        <div class="user-info">
            <span id="welcomeMessage">مرحباً بك!</span>
            <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
        </div>
    </div>

    <div class="container">
        <div id="alert" class="alert" style="display: none;"></div>

        <div class="cards">
            <div class="card">
                <h3>🔐 خدمة المصادقة</h3>
                <p>إدارة تسجيل الدخول والمستخدمين</p>
                <button class="btn" onclick="testAuthService()">اختبار الخدمة</button>
                <button class="btn btn-success" onclick="refreshToken()">تجديد الرمز المميز</button>
            </div>

            <div class="card">
                <h3>👤 خدمة المستخدمين</h3>
                <p>عرض وإدارة بيانات المستخدمين</p>
                <button class="btn" onclick="getUserProfile()">عرض الملف الشخصي</button>
                <button class="btn" onclick="updateProfile()">تحديث البيانات</button>
            </div>

            <div class="card">
                <h3>⚙️ إدارة النظام</h3>
                <p>إعدادات وإدارة النظام</p>
                <button class="btn" onclick="checkSystemStatus()">حالة النظام</button>
                <button class="btn btn-success" onclick="viewLogs()">عرض السجلات</button>
            </div>

            <div class="card">
                <h3>📊 الإحصائيات</h3>
                <p>إحصائيات الاستخدام والأداء</p>
                <button class="btn" onclick="viewStats()">عرض الإحصائيات</button>
                <button class="btn btn-success" onclick="generateReport()">تقرير شامل</button>
            </div>
        </div>

        <div class="card">
            <h3>📊 حالة الخدمات</h3>
            <div id="servicesContainer">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>جاري فحص حالة الخدمات...</p>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>📈 نشاط النظام</h3>
            <div id="activityContainer">
                <p>📅 تاريخ آخر دخول: <span id="lastLogin">جاري التحميل...</span></p>
                <p>🕒 وقت تشغيل النظام: <span id="uptime">جاري التحميل...</span></p>
                <p>👥 عدد المستخدمين النشطين: <span id="activeUsers">جاري التحميل...</span></p>
                <p>🔄 آخر عملية: <span id="lastActivity">جاري التحميل...</span></p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001';
        const PRODUCT_API = 'http://localhost:8003';
        const USER_API = 'http://localhost:8002';
        const ORDER_API = 'http://localhost:8004';

        // Check authentication on page load
        window.onload = function() {
            const token = localStorage.getItem('authToken');
            const userId = localStorage.getItem('userId');

            if (!token) {
                window.location.href = 'login.html';
                return;
            }

            document.getElementById('welcomeMessage').textContent = `مرحباً! (المستخدم: ${userId})`;
            checkServicesStatus();
            loadSystemActivity();
        };

        async function makeAuthenticatedRequest(url, options = {}) {
            const token = localStorage.getItem('authToken');
            
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            };
            
            return fetch(url, { ...defaultOptions, ...options });
        }

        async function testAuthService() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    showAlert('✅ خدمة المصادقة تعمل بشكل طبيعي', 'success');
                } else {
                    showAlert('⚠️ مشكلة في خدمة المصادقة', 'error');
                }
            } catch (error) {
                showAlert('❌ خطأ في الاتصال بخدمة المصادقة', 'error');
            }
        }

        async function checkServicesStatus() {
            const services = [
                { name: 'خدمة المصادقة', url: `${API_BASE}/health`, icon: '🔐' },
                { name: 'خدمة المستخدمين', url: `${USER_API}/health`, icon: '👤' },
                { name: 'خدمة المنتجات', url: `${PRODUCT_API}/health`, icon: '🛍️' },
                { name: 'خدمة الطلبات', url: `${ORDER_API}/health`, icon: '📦' }
            ];

            let statusHTML = '<div class="services-grid">';

            for (const service of services) {
                try {
                    const response = await fetch(service.url);
                    const data = await response.json();

                    if (data.status === 'healthy') {
                        statusHTML += `
                            <div class="service-status healthy">
                                ${service.icon} ${service.name}: <span style="color: #28a745;">✅ يعمل</span>
                            </div>
                        `;
                    } else {
                        statusHTML += `
                            <div class="service-status error">
                                ${service.icon} ${service.name}: <span style="color: #dc3545;">❌ مشكلة</span>
                            </div>
                        `;
                    }
                } catch (error) {
                    statusHTML += `
                        <div class="service-status error">
                            ${service.icon} ${service.name}: <span style="color: #dc3545;">❌ غير متاح</span>
                        </div>
                    `;
                }
            }

            statusHTML += '</div>';
            document.getElementById('servicesContainer').innerHTML = statusHTML;
        }

        function loadSystemActivity() {
            // Simulate system activity data
            const now = new Date();
            const loginTime = new Date(now.getTime() - Math.random() * 3600000); // Random time in last hour

            document.getElementById('lastLogin').textContent = loginTime.toLocaleString('ar-SA');
            document.getElementById('uptime').textContent = '2 ساعة و 15 دقيقة';
            document.getElementById('activeUsers').textContent = Math.floor(Math.random() * 10) + 1;
            document.getElementById('lastActivity').textContent = 'تسجيل دخول مستخدم جديد';
        }

        async function getUserProfile() {
            const userId = localStorage.getItem('userId');
            
            try {
                const response = await makeAuthenticatedRequest(`${USER_API}/?user_id=${userId}`);
                const data = await response.json();
                
                if (data.status === 'success') {
                    alert(`الملف الشخصي:\nالاسم: ${data.data.name}\nالبريد: ${data.data.email}`);
                } else {
                    showAlert('❌ فشل في تحميل الملف الشخصي', 'error');
                }
            } catch (error) {
                showAlert('❌ خطأ في تحميل الملف الشخصي', 'error');
            }
        }

        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('userId');
            window.location.href = 'login.html';
        }

        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert alert-${type}`;
            alert.style.display = 'block';
            
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }

        // System management functions
        function checkSystemStatus() {
            showAlert('🔄 جاري فحص حالة النظام...', 'success');
            checkServicesStatus();
            loadSystemActivity();
        }

        function viewLogs() {
            showAlert('📋 عرض سجلات النظام:\n- آخر دخول: منذ 5 دقائق\n- آخر خطأ: لا يوجد\n- حالة الخدمات: جميعها تعمل', 'success');
        }

        function viewStats() {
            const stats = `📊 إحصائيات النظام:
• عدد المستخدمين: ${Math.floor(Math.random() * 100) + 50}
• عدد العمليات اليوم: ${Math.floor(Math.random() * 500) + 100}
• متوسط وقت الاستجابة: ${Math.floor(Math.random() * 100) + 50}ms
• استخدام الذاكرة: ${Math.floor(Math.random() * 30) + 40}%`;

            showAlert(stats, 'success');
        }

        function generateReport() {
            showAlert('📄 تم إنشاء التقرير الشامل بنجاح!\n\nيحتوي التقرير على:\n• إحصائيات الاستخدام\n• حالة الخدمات\n• سجلات الأنشطة\n• توصيات التحسين', 'success');
        }

        function refreshToken() {
            showAlert('🔄 جاري تجديد الرمز المميز...', 'success');
        }

        function updateProfile() {
            const newName = prompt('أدخل الاسم الجديد:');
            if (newName) {
                showAlert(`✅ تم تحديث الاسم إلى: ${newName}`, 'success');
            }
        }
    </script>
</body>
</html>
