@echo off
title Microservices Launcher
color 0A

echo.
echo  ███╗   ███╗██╗ ██████╗██████╗  ██████╗ ███████╗███████╗██████╗ ██╗   ██╗██╗ ██████╗███████╗███████╗
echo  ████╗ ████║██║██╔════╝██╔══██╗██╔═══██╗██╔════╝██╔════╝██╔══██╗██║   ██║██║██╔════╝██╔════╝██╔════╝
echo  ██╔████╔██║██║██║     ██████╔╝██║   ██║███████╗█████╗  ██████╔╝██║   ██║██║██║     █████╗  ███████╗
echo  ██║╚██╔╝██║██║██║     ██╔══██╗██║   ██║╚════██║██╔══╝  ██╔══██╗╚██╗ ██╔╝██║██║     ██╔══╝  ╚════██║
echo  ██║ ╚═╝ ██║██║╚██████╗██║  ██║╚██████╔╝███████║███████╗██║  ██║ ╚████╔╝ ██║╚██████╗███████╗███████║
echo  ╚═╝     ╚═╝╚═╝ ╚═════╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚═╝ ╚═════╝╚══════╝╚══════╝
echo.
echo                                    PHP + MySQL Microservices
echo                                         XAMPP Edition
echo.

echo 🔍 Checking prerequisites...

:: Check if PHP is available
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PHP not found! Please install PHP or add it to PATH
    pause
    exit /b 1
)
echo ✅ PHP is available

:: Check if MySQL is running
netstat -an | findstr :3306 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL not running! Please start MySQL in XAMPP Control Panel
    pause
    exit /b 1
)
echo ✅ MySQL is running

echo.
echo 🗄️ Setting up database...
php setup_xampp.php
if %errorlevel% neq 0 (
    echo ❌ Database setup failed!
    pause
    exit /b 1
)

echo.
echo 🚀 Starting microservices...
echo.

:: Start services in separate windows
echo 🔐 Starting Auth Service (Port 8001)...
start "🔐 Auth Service" cmd /k "echo Auth Service Running on http://localhost:8001 && php -S localhost:8001 -t auth-service"

timeout /t 2 /nobreak >nul

echo 👤 Starting User Service (Port 8002)...
start "👤 User Service" cmd /k "echo User Service Running on http://localhost:8002 && php -S localhost:8002 -t user-service"

timeout /t 2 /nobreak >nul

echo 🛍️ Starting Product Service (Port 8003)...
start "🛍️ Product Service" cmd /k "echo Product Service Running on http://localhost:8003 && php -S localhost:8003 -t product-service"

timeout /t 2 /nobreak >nul

echo 📦 Starting Order Service (Port 8004)...
start "📦 Order Service" cmd /k "echo Order Service Running on http://localhost:8004 && php -S localhost:8004 -t order-service"

echo.
echo ⏳ Waiting for services to start...
timeout /t 5 /nobreak >nul

echo.
echo ✅ All services started successfully!
echo.
echo 📍 Service URLs:
echo    🔐 Auth Service:    http://localhost:8001
echo    👤 User Service:    http://localhost:8002
echo    🛍️ Product Service: http://localhost:8003
echo    📦 Order Service:   http://localhost:8004
echo.
echo 🧪 Testing services...
echo.

:: Test each service
for %%s in (8001 8002 8003 8004) do (
    curl -s http://localhost:%%s/health >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ Service on port %%s: OK
    ) else (
        echo ❌ Service on port %%s: Not responding
    )
)

echo.
echo 🎉 Setup complete!
echo.
echo 💡 Next steps:
echo    1. Test APIs: php tests/api_test_xampp.php
echo    2. Open browser: http://localhost:8001/health
echo    3. Use Postman for API testing
echo    4. Check phpMyAdmin for database
echo.
echo 📚 Documentation: README_XAMPP.md
echo.
echo Press any key to continue...
pause >nul
