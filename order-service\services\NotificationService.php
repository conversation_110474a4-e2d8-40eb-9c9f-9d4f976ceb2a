<?php
class NotificationService {
    private $notificationServiceUrl;
    private $emailEnabled;
    private $smsEnabled;
    
    public function __construct() {
        $this->notificationServiceUrl = getenv('NOTIFICATION_SERVICE_URL') ?: 'http://notification-service';
        $this->emailEnabled = getenv('EMAIL_NOTIFICATIONS') === 'true';
        $this->smsEnabled = getenv('SMS_NOTIFICATIONS') === 'true';
    }
    
    public function sendOrderConfirmation($userId, $orderId) {
        $message = [
            'type' => 'order_confirmation',
            'user_id' => $userId,
            'order_id' => $orderId,
            'title' => 'Order Confirmation',
            'message' => "Your order #$orderId has been confirmed and is being processed.",
            'channels' => $this->getEnabledChannels()
        ];
        
        return $this->sendNotification($message);
    }
    
    public function sendOrderStatusUpdate($userId, $orderId, $status) {
        $statusMessages = [
            'confirmed' => 'Your order has been confirmed',
            'shipped' => 'Your order has been shipped',
            'delivered' => 'Your order has been delivered',
            'cancelled' => 'Your order has been cancelled'
        ];
        
        $message = [
            'type' => 'order_status_update',
            'user_id' => $userId,
            'order_id' => $orderId,
            'title' => 'Order Status Update',
            'message' => $statusMessages[$status] ?? "Your order status has been updated to: $status",
            'channels' => $this->getEnabledChannels()
        ];
        
        return $this->sendNotification($message);
    }
    
    public function sendOrderCancellation($userId, $orderId) {
        $message = [
            'type' => 'order_cancellation',
            'user_id' => $userId,
            'order_id' => $orderId,
            'title' => 'Order Cancelled',
            'message' => "Your order #$orderId has been cancelled successfully.",
            'channels' => $this->getEnabledChannels()
        ];
        
        return $this->sendNotification($message);
    }
    
    public function sendLowStockAlert($productId, $currentStock) {
        $message = [
            'type' => 'low_stock_alert',
            'product_id' => $productId,
            'current_stock' => $currentStock,
            'title' => 'Low Stock Alert',
            'message' => "Product #$productId is running low on stock. Current stock: $currentStock",
            'channels' => ['email'] // Admin notifications
        ];
        
        return $this->sendNotification($message);
    }
    
    private function sendNotification($message) {
        // If notification service is available, use it
        if ($this->notificationServiceUrl) {
            return $this->sendToNotificationService($message);
        }
        
        // Fallback to simple logging
        return $this->logNotification($message);
    }
    
    private function sendToNotificationService($message) {
        $data = json_encode($message);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->notificationServiceUrl . '/send');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return $httpCode === 200;
    }
    
    private function logNotification($message) {
        $logDir = '/var/log/notifications';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0777, true);
        }
        
        $logFile = $logDir . '/notifications.log';
        $logEntry = date('Y-m-d H:i:s') . ' - ' . json_encode($message) . PHP_EOL;
        
        return file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX) !== false;
    }
    
    private function getEnabledChannels() {
        $channels = [];
        
        if ($this->emailEnabled) {
            $channels[] = 'email';
        }
        
        if ($this->smsEnabled) {
            $channels[] = 'sms';
        }
        
        // Always include in-app notifications
        $channels[] = 'in_app';
        
        return $channels;
    }
}
