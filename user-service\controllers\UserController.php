<?php
class UserController {
    private $db;
    private $user;

    public function __construct() {
        $database = new Database();
        $this->db = $database->connect();
        $this->user = new User($this->db);
    }

    public function getProfile($userId) {
        $userData = $this->user->getById($userId);
        if ($userData) {
            return json_encode(['status' => 'success', 'data' => $userData]);
        }
        return json_encode(['status' => 'error', 'message' => 'User not found']);
    }

    public function updateProfile($userId, $data) {
        if ($this->user->update($userId, $data)) {
            return json_encode(['status' => 'success', 'message' => 'Profile updated successfully']);
        }
        return json_encode(['status' => 'error', 'message' => 'Failed to update profile']);
    }

    public function listUsers($page = 1, $limit = 10) {
        $users = $this->user->getAll($page, $limit);
        return json_encode(['status' => 'success', 'data' => $users]);
    }
}
