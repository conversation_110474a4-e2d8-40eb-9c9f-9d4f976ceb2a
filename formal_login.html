<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الشركة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .login-wrapper {
            background: #ffffff;
            width: 100%;
            max-width: 900px;
            min-height: 600px;
            display: flex;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            border-radius: 0;
        }

        .login-left {
            flex: 1;
            background: #2c3e50;
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .company-logo {
            width: 80px;
            height: 80px;
            background: #3498db;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-bottom: 30px;
        }

        .company-info h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #ffffff;
        }

        .company-info p {
            font-size: 16px;
            line-height: 1.6;
            color: #bdc3c7;
            margin-bottom: 20px;
        }

        .features-list {
            list-style: none;
            margin-top: 30px;
        }

        .features-list li {
            padding: 8px 0;
            color: #ecf0f1;
            font-size: 14px;
        }

        .features-list li:before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            margin-left: 10px;
        }

        .login-right {
            flex: 1;
            padding: 60px 50px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-header h2 {
            font-size: 24px;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .login-header p {
            color: #7f8c8d;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
            direction: ltr;
            text-align: left;
            background: #ffffff;
            color: #2c3e50;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3498db;
        }

        .form-group input::placeholder {
            color: #95a5a6;
        }

        .login-btn {
            width: 100%;
            padding: 14px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            background: #2980b9;
        }

        .login-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .forgot-password {
            text-align: center;
            margin-bottom: 30px;
        }

        .forgot-password a {
            color: #3498db;
            text-decoration: none;
            font-size: 14px;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        .alert {
            padding: 12px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
            border: 1px solid;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .demo-accounts {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 20px;
            margin-top: 20px;
        }

        .demo-accounts h4 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 14px;
            font-weight: 600;
        }

        .demo-account {
            background: #ffffff;
            border: 1px solid #dee2e6;
            padding: 10px 15px;
            margin: 8px 0;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-size: 13px;
        }

        .demo-account:hover {
            background: #e9ecef;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 15px 0;
        }

        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 12px;
        }

        @media (max-width: 768px) {
            .login-wrapper {
                flex-direction: column;
                margin: 20px;
                max-width: none;
            }
            
            .login-left {
                padding: 40px 30px;
            }
            
            .login-right {
                padding: 40px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="login-wrapper">
        <div class="login-left">
            <div class="company-logo">🏢</div>
            <div class="company-info">
                <h1>نظام إدارة الشركة</h1>
                <p>منصة إدارية متكاملة لإدارة جميع عمليات الشركة بكفاءة وأمان عالي</p>
                
                <ul class="features-list">
                    <li>إدارة المستخدمين والصلاحيات</li>
                    <li>نظام أمان متقدم ومشفر</li>
                    <li>تقارير وإحصائيات شاملة</li>
                    <li>واجهة سهلة الاستخدام</li>
                    <li>دعم فني على مدار الساعة</li>
                </ul>
            </div>
        </div>
        
        <div class="login-right">
            <div class="login-header">
                <h2>تسجيل الدخول</h2>
                <p>أدخل بياناتك للوصول إلى النظام</p>
            </div>
            
            <div id="alert" class="alert" style="display: none;"></div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" required placeholder="أدخل كلمة المرور">
                </div>
                
                <button type="submit" class="login-btn" id="loginBtn">
                    تسجيل الدخول
                </button>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>جاري تسجيل الدخول...</p>
                </div>
            </form>
            
            <div class="forgot-password">
                <a href="#">نسيت كلمة المرور؟</a>
            </div>
            
            <div class="demo-accounts">
                <h4>حسابات تجريبية للاختبار:</h4>
                <div class="demo-account" onclick="fillLogin('<EMAIL>', 'password')">
                    <strong>حساب المدير:</strong> <EMAIL>
                </div>
                <div class="demo-account" onclick="fillLogin('<EMAIL>', 'password')">
                    <strong>حساب المستخدم:</strong> <EMAIL>
                </div>
            </div>
            
            <div class="footer">
                <p>&copy; 2024 نظام إدارة الشركة. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001';
        
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loading = document.getElementById('loading');
            
            // Show loading
            loginBtn.disabled = true;
            loading.style.display = 'block';
            hideAlert();
            
            try {
                const response = await fetch(`${API_BASE}/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    // Save token
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('refreshToken', data.refresh_token);
                    localStorage.setItem('userId', data.user_id);
                    
                    showAlert('تم تسجيل الدخول بنجاح! جاري التوجيه...', 'success');
                    
                    // Redirect to formal dashboard after 2 seconds
                    setTimeout(() => {
                        window.location.href = 'formal_dashboard.html';
                    }, 2000);
                    
                } else {
                    showAlert('خطأ في تسجيل الدخول: ' + data.message, 'error');
                }
                
            } catch (error) {
                showAlert('خطأ في الاتصال: ' + error.message, 'error');
            } finally {
                loginBtn.disabled = false;
                loading.style.display = 'none';
            }
        });
        
        function fillLogin(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
        }
        
        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert alert-${type}`;
            alert.style.display = 'block';
        }
        
        function hideAlert() {
            document.getElementById('alert').style.display = 'none';
        }
        
        // Check if already logged in
        if (localStorage.getItem('authToken')) {
            showAlert('أنت مسجل دخول بالفعل، جاري التوجيه...', 'success');
            setTimeout(() => {
                window.location.href = 'formal_dashboard.html';
            }, 1000);
        }
    </script>
</body>
</html>
