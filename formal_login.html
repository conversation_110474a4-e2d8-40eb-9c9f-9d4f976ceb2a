<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الشركة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .login-wrapper {
            background: #ffffff;
            width: 100%;
            max-width: 900px;
            min-height: 600px;
            display: flex;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            border-radius: 0;
        }

        .login-left {
            flex: 1;
            background: #2c3e50;
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .company-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
        }

        .company-logo::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            bottom: 2px;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
            border-radius: 14px;
            pointer-events: none;
        }

        .company-info h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #ffffff;
        }

        .company-info p {
            font-size: 16px;
            line-height: 1.6;
            color: #bdc3c7;
            margin-bottom: 20px;
        }

        .features-list {
            list-style: none;
            margin-top: 30px;
        }

        .features-list li {
            padding: 8px 0;
            color: #ecf0f1;
            font-size: 14px;
        }

        .features-list li:before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            margin-left: 10px;
        }

        .login-right {
            flex: 1;
            padding: 60px 50px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-header h2 {
            font-size: 24px;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .login-header p {
            color: #7f8c8d;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
            direction: ltr;
            text-align: left;
            background: #ffffff;
            color: #2c3e50;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3498db;
        }

        .form-group input::placeholder {
            color: #95a5a6;
        }

        .login-btn {
            width: 100%;
            padding: 14px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            background: #2980b9;
        }

        .login-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .forgot-password {
            text-align: center;
            margin-bottom: 30px;
        }

        .forgot-password a {
            color: #3498db;
            text-decoration: none;
            font-size: 14px;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        .alert {
            padding: 12px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
            border: 1px solid;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .demo-accounts {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 20px;
            margin-top: 20px;
        }

        .demo-accounts h4 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 14px;
            font-weight: 600;
        }

        .demo-account {
            background: #ffffff;
            border: 1px solid #dee2e6;
            padding: 10px 15px;
            margin: 8px 0;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-size: 13px;
        }

        .demo-account:hover {
            background: #e9ecef;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 15px 0;
        }

        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 12px;
        }

        @media (max-width: 768px) {
            .login-wrapper {
                flex-direction: column;
                margin: 20px;
                max-width: none;
            }
            
            .login-left {
                padding: 40px 30px;
            }
            
            .login-right {
                padding: 40px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="login-wrapper">
        <div class="login-left">
            <div class="company-logo">
                <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="white" stroke-width="2" stroke-linejoin="round"/>
                    <path d="M2 17L12 22L22 17" stroke="white" stroke-width="2" stroke-linejoin="round"/>
                    <path d="M2 12L12 17L22 12" stroke="white" stroke-width="2" stroke-linejoin="round"/>
                </svg>
            </div>
            <div class="company-info">
                <h1>نظام إدارة الشركة</h1>
                <p>منصة إدارية متكاملة لإدارة جميع عمليات الشركة بكفاءة وأمان عالي</p>
                
                <ul class="features-list">
                    <li>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" style="display: inline; margin-left: 8px;">
                            <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="#27ae60" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="12" cy="7" r="4" stroke="#27ae60" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        إدارة المستخدمين والصلاحيات
                    </li>
                    <li>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" style="display: inline; margin-left: 8px;">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="#27ae60" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="12" cy="16" r="1" fill="#27ae60"/>
                            <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="#27ae60" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        نظام أمان متقدم ومشفر
                    </li>
                    <li>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" style="display: inline; margin-left: 8px;">
                            <path d="M3 3V21H21" stroke="#27ae60" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M9 9L12 6L16 10L21 5" stroke="#27ae60" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        تقارير وإحصائيات شاملة
                    </li>
                    <li>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" style="display: inline; margin-left: 8px;">
                            <circle cx="12" cy="12" r="3" stroke="#27ae60" stroke-width="2"/>
                            <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2573 9.77251 19.9887C9.5799 19.7201 9.31074 19.5176 9 19.41C8.69838 19.2769 8.36381 19.2372 8.03941 19.296C7.71502 19.3548 7.41568 19.5095 7.18 19.74L7.12 19.8C6.93425 19.986 6.71368 20.1335 6.47088 20.2341C6.22808 20.3348 5.96783 20.3866 5.705 20.3866C5.44217 20.3866 5.18192 20.3348 4.93912 20.2341C4.69632 20.1335 4.47575 19.986 4.29 19.8C4.10405 19.6143 3.95653 19.3937 3.85588 19.1509C3.75523 18.9081 3.70343 18.6478 3.70343 18.385C3.70343 18.1222 3.75523 17.8619 3.85588 17.6191C3.95653 17.3763 4.10405 17.1557 4.29 16.97L4.35 16.91C4.58054 16.6743 4.73519 16.375 4.794 16.0506C4.85282 15.7262 4.81312 15.3916 4.68 15.09C4.55324 14.7942 4.34276 14.542 4.07447 14.3643C3.80618 14.1866 3.49179 14.0913 3.17 14.09H3C2.46957 14.09 1.96086 13.8793 1.58579 13.5042C1.21071 13.1291 1 12.6204 1 12.09C1 11.5596 1.21071 11.0509 1.58579 10.6758C1.96086 10.3007 2.46957 10.09 3 10.09H3.09C3.42099 10.0823 3.742 9.97512 4.01062 9.78251C4.27925 9.5899 4.48167 9.32074 4.59 9.01C4.72312 8.70838 4.76282 8.37381 4.704 8.04941C4.64519 7.72502 4.49054 7.42568 4.26 7.19L4.2 7.13C4.01405 6.94425 3.86653 6.72368 3.76588 6.48088C3.66523 6.23808 3.61343 5.97783 3.61343 5.715C3.61343 5.45217 3.66523 5.19192 3.76588 4.94912C3.86653 4.70632 4.01405 4.48575 4.2 4.3C4.38575 4.11405 4.60632 3.96653 4.84912 3.86588C5.09192 3.76523 5.35217 3.71343 5.615 3.71343C5.87783 3.71343 6.13808 3.76523 6.38088 3.86588C6.62368 3.96653 6.84425 4.11405 7.03 4.3L7.09 4.36C7.32568 4.59054 7.62502 4.74519 7.94941 4.804C8.27381 4.86282 8.60838 4.82312 8.91 4.69H9C9.29577 4.56324 9.54802 4.35276 9.72569 4.08447C9.90337 3.81618 9.99872 3.50179 10 3.18V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="#27ae60" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        واجهة سهلة الاستخدام
                    </li>
                    <li>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" style="display: inline; margin-left: 8px;">
                            <path d="M22 16.92V7.08C21.9996 6.71003 21.9071 6.34687 21.731 6.02C21.5549 5.69312 21.3001 5.41345 20.99 5.2L13.99 0.68C13.6718 0.462477 13.2956 0.345703 12.91 0.345703C12.5244 0.345703 12.1482 0.462477 11.83 0.68L4.83 5.2C4.51994 5.41345 4.26513 5.69312 4.08902 6.02C3.91292 6.34687 3.82041 6.71003 3.82 7.08V16.92C3.82041 17.29 3.91292 17.6531 4.08902 17.98C4.26513 18.3069 4.51994 18.5865 4.83 18.8L11.83 23.32C12.1482 23.5375 12.5244 23.6543 12.91 23.6543C13.2956 23.6543 13.6718 23.5375 13.99 23.32L20.99 18.8C21.3001 18.5865 21.5549 18.3069 21.731 17.98C21.9071 17.6531 21.9996 17.29 22 16.92Z" stroke="#27ae60" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M12 8C13.6569 8 15 6.65685 15 5C15 3.34315 13.6569 2 12 2C10.3431 2 9 3.34315 9 5C9 6.65685 10.3431 8 12 8Z" stroke="#27ae60" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M12 22V8" stroke="#27ae60" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        دعم فني على مدار الساعة
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="login-right">
            <div class="login-header">
                <h2>تسجيل الدخول</h2>
                <p>أدخل بياناتك للوصول إلى النظام</p>
            </div>
            
            <div id="alert" class="alert" style="display: none;"></div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" required placeholder="أدخل كلمة المرور">
                </div>
                
                <button type="submit" class="login-btn" id="loginBtn">
                    تسجيل الدخول
                </button>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>جاري تسجيل الدخول...</p>
                </div>
            </form>
            
            <div class="forgot-password">
                <a href="#">نسيت كلمة المرور؟</a>
            </div>
            
            <div class="demo-accounts">
                <h4>حسابات تجريبية للاختبار:</h4>
                <div class="demo-account" onclick="fillLogin('<EMAIL>', 'password')">
                    <strong>حساب المدير:</strong> <EMAIL>
                </div>
                <div class="demo-account" onclick="fillLogin('<EMAIL>', 'password')">
                    <strong>حساب المستخدم:</strong> <EMAIL>
                </div>
            </div>
            
            <div class="footer">
                <p>&copy; 2024 نظام إدارة الشركة. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001';
        
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loading = document.getElementById('loading');
            
            // Show loading
            loginBtn.disabled = true;
            loading.style.display = 'block';
            hideAlert();
            
            try {
                const response = await fetch(`${API_BASE}/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    // Save token
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('refreshToken', data.refresh_token);
                    localStorage.setItem('userId', data.user_id);
                    
                    showAlert('تم تسجيل الدخول بنجاح! جاري التوجيه...', 'success');
                    
                    // Redirect to formal dashboard after 2 seconds
                    setTimeout(() => {
                        window.location.href = 'formal_dashboard.html';
                    }, 2000);
                    
                } else {
                    showAlert('خطأ في تسجيل الدخول: ' + data.message, 'error');
                }
                
            } catch (error) {
                showAlert('خطأ في الاتصال: ' + error.message, 'error');
            } finally {
                loginBtn.disabled = false;
                loading.style.display = 'none';
            }
        });
        
        function fillLogin(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
        }
        
        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert alert-${type}`;
            alert.style.display = 'block';
        }
        
        function hideAlert() {
            document.getElementById('alert').style.display = 'none';
        }
        
        // Check if already logged in
        if (localStorage.getItem('authToken')) {
            showAlert('أنت مسجل دخول بالفعل، جاري التوجيه...', 'success');
            setTimeout(() => {
                window.location.href = 'formal_dashboard.html';
            }, 1000);
        }
    </script>
</body>
</html>
