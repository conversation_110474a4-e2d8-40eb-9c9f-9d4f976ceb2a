{"title": "Microservices API Documentation", "version": "1.0.0", "base_url": "http://localhost", "services": {"auth": {"port": 8001, "endpoints": {"POST /": "Authentication operations (login, register, refresh, logout)", "GET /health": "Service health check"}}, "user": {"port": 8002, "endpoints": {"GET /?user_id={id}": "Get user profile", "PUT /?user_id={id}": "Update user profile", "GET /health": "Service health check"}}, "product": {"port": 8003, "endpoints": {"GET /": "List all products", "GET /?id={id}": "Get specific product", "POST /": "Create new product (auth required)", "PUT /?id={id}": "Update product (auth required)", "DELETE /?id={id}": "Delete product (auth required)", "GET /health": "Service health check"}}, "order": {"port": 8004, "endpoints": {"GET /": "List user orders (auth required)", "GET /{id}": "Get specific order (auth required)", "POST /": "Create new order (auth required)", "PUT /{id}": "Update order status (auth required)", "DELETE /{id}": "Cancel order (auth required)", "GET /health": "Service health check"}}}}