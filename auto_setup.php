<?php
/**
 * Auto Setup Script for Microservices
 * Automatically configures database and system
 */

echo "🚀 Auto-Setup: Microservices Database Configuration\n";
echo "==================================================\n\n";

// Configuration
$config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'microservices_db'
];

// Step 1: Test MySQL Connection
echo "📡 Step 1: Testing MySQL connection...\n";
try {
    $pdo = new PDO("mysql:host={$config['host']}", $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ MySQL connection successful\n\n";
} catch (PDOException $e) {
    echo "❌ MySQL connection failed: " . $e->getMessage() . "\n";
    echo "💡 Please ensure MySQL is running in XAMPP\n";
    exit(1);
}

// Step 2: Create Database
echo "🗄️ Step 2: Creating database...\n";
try {
    $pdo->exec("CREATE DATABASE IF NOT EXISTS {$config['database']}");
    $pdo->exec("USE {$config['database']}");
    echo "✅ Database '{$config['database']}' created/selected\n\n";
} catch (PDOException $e) {
    echo "❌ Database creation failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Step 3: Create Tables
echo "📋 Step 3: Creating tables...\n";
try {
    $sql = file_get_contents('init.sql');
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $tableCount = 0;
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^--/', trim($statement))) {
            try {
                $pdo->exec($statement);
                if (stripos($statement, 'CREATE TABLE') !== false) {
                    $tableCount++;
                    preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $statement, $matches);
                    $tableName = $matches[1] ?? 'unknown';
                    echo "  ✓ Table '$tableName' created\n";
                }
            } catch (PDOException $e) {
                // Ignore duplicate entry errors for sample data
                if (strpos($e->getMessage(), 'Duplicate entry') === false && 
                    strpos($e->getMessage(), 'already exists') === false) {
                    throw $e;
                }
            }
        }
    }
    echo "✅ Created $tableCount tables successfully\n\n";
} catch (Exception $e) {
    echo "❌ Table creation failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Step 4: Verify Data
echo "🧪 Step 4: Verifying sample data...\n";
try {
    $tables = ['users', 'products', 'orders', 'order_items', 'notifications'];
    
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "  📊 Table '$table': {$result['count']} records\n";
    }
    echo "✅ Data verification completed\n\n";
} catch (PDOException $e) {
    echo "⚠️ Data verification warning: " . $e->getMessage() . "\n\n";
}

// Step 5: Create Admin User
echo "👤 Step 5: Setting up admin user...\n";
try {
    $adminEmail = '<EMAIL>';
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$adminEmail]);
    
    if (!$stmt->fetch()) {
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password) VALUES (?, ?, ?)");
        $stmt->execute(['System Admin', $adminEmail, $adminPassword]);
        echo "✅ Admin user created\n";
        echo "  📧 Email: $adminEmail\n";
        echo "  🔑 Password: admin123\n";
    } else {
        echo "✅ Admin user already exists\n";
    }
    echo "\n";
} catch (PDOException $e) {
    echo "⚠️ Admin user setup warning: " . $e->getMessage() . "\n\n";
}

// Step 6: Create Configuration File
echo "⚙️ Step 6: Creating configuration files...\n";
$configData = [
    'database' => $config,
    'jwt_secret' => bin2hex(random_bytes(32)),
    'services' => [
        'auth' => 'http://localhost:8001',
        'user' => 'http://localhost:8002',
        'product' => 'http://localhost:8003',
        'order' => 'http://localhost:8004'
    ],
    'setup_date' => date('Y-m-d H:i:s'),
    'version' => '1.0.0'
];

file_put_contents('.env', "# Auto-generated configuration\n");
file_put_contents('.env', "MYSQL_HOST={$config['host']}\n", FILE_APPEND);
file_put_contents('.env', "MYSQL_DATABASE={$config['database']}\n", FILE_APPEND);
file_put_contents('.env', "MYSQL_USER={$config['username']}\n", FILE_APPEND);
file_put_contents('.env', "MYSQL_PASSWORD={$config['password']}\n", FILE_APPEND);
file_put_contents('.env', "JWT_SECRET={$configData['jwt_secret']}\n", FILE_APPEND);

file_put_contents('config.json', json_encode($configData, JSON_PRETTY_PRINT));
echo "✅ Configuration files created\n\n";

// Step 7: Test Database Operations
echo "🔬 Step 7: Testing database operations...\n";
try {
    // Test user creation
    $testEmail = 'test_' . time() . '@example.com';
    $stmt = $pdo->prepare("INSERT INTO users (name, email, password) VALUES (?, ?, ?)");
    $stmt->execute(['Test User', $testEmail, password_hash('test123', PASSWORD_DEFAULT)]);
    $userId = $pdo->lastInsertId();
    echo "  ✓ User creation test passed\n";
    
    // Test product query
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products WHERE stock > 0");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "  ✓ Product query test passed ({$result['count']} products in stock)\n";
    
    // Clean up test data
    $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    echo "  ✓ Database cleanup completed\n";
    
    echo "✅ All database operations working correctly\n\n";
} catch (PDOException $e) {
    echo "❌ Database operation test failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Step 8: Generate API Documentation
echo "📚 Step 8: Generating API documentation...\n";
$apiDocs = [
    'title' => 'Microservices API Documentation',
    'version' => '1.0.0',
    'base_url' => 'http://localhost',
    'services' => [
        'auth' => [
            'port' => 8001,
            'endpoints' => [
                'POST /' => 'Authentication operations (login, register, refresh, logout)',
                'GET /health' => 'Service health check'
            ]
        ],
        'user' => [
            'port' => 8002,
            'endpoints' => [
                'GET /?user_id={id}' => 'Get user profile',
                'PUT /?user_id={id}' => 'Update user profile',
                'GET /health' => 'Service health check'
            ]
        ],
        'product' => [
            'port' => 8003,
            'endpoints' => [
                'GET /' => 'List all products',
                'GET /?id={id}' => 'Get specific product',
                'POST /' => 'Create new product (auth required)',
                'PUT /?id={id}' => 'Update product (auth required)',
                'DELETE /?id={id}' => 'Delete product (auth required)',
                'GET /health' => 'Service health check'
            ]
        ],
        'order' => [
            'port' => 8004,
            'endpoints' => [
                'GET /' => 'List user orders (auth required)',
                'GET /{id}' => 'Get specific order (auth required)',
                'POST /' => 'Create new order (auth required)',
                'PUT /{id}' => 'Update order status (auth required)',
                'DELETE /{id}' => 'Cancel order (auth required)',
                'GET /health' => 'Service health check'
            ]
        ]
    ]
];

file_put_contents('api_docs.json', json_encode($apiDocs, JSON_PRETTY_PRINT));
echo "✅ API documentation generated\n\n";

echo "🎉 AUTO-SETUP COMPLETED SUCCESSFULLY!\n";
echo "=====================================\n\n";

echo "📊 Setup Summary:\n";
echo "  ✓ Database: {$config['database']}\n";
echo "  ✓ Tables: Created with sample data\n";
echo "  ✓ Admin User: <EMAIL> / admin123\n";
echo "  ✓ Configuration: Generated\n";
echo "  ✓ API Docs: Generated\n\n";

echo "🚀 System is ready for service startup!\n";
echo "💡 Next: Services will be started automatically...\n\n";

exit(0);
?>
