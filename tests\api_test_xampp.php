<?php
/**
 * API Testing Script for XAMPP Environment
 * Run with: php tests/api_test_xampp.php
 */

class XAMPPAPITester {
    private $services;
    private $token;
    
    public function __construct() {
        $this->services = [
            'auth' => 'http://localhost:8001',
            'user' => 'http://localhost:8002',
            'product' => 'http://localhost:8003',
            'order' => 'http://localhost:8004'
        ];
    }
    
    public function runAllTests() {
        echo "🚀 Starting XAMPP API Tests...\n\n";
        
        $this->testHealthChecks();
        $this->testDatabase();
        $this->testAuthentication();
        $this->testProductService();
        $this->testUserService();
        $this->testOrderService();
        
        echo "\n✅ All tests completed!\n";
    }
    
    private function testHealthChecks() {
        echo "🏥 Testing Health Checks...\n";
        
        foreach ($this->services as $name => $url) {
            $response = $this->makeRequest('GET', $url . '/health');
            if ($response && isset($response['status']) && $response['status'] === 'healthy') {
                echo "  ✅ $name Service: Healthy\n";
            } else {
                echo "  ❌ $name Service: Unhealthy or not running\n";
                echo "     💡 Make sure to run: xampp_setup.bat\n";
            }
        }
        echo "\n";
    }
    
    private function testDatabase() {
        echo "🗄️ Testing Database Connection...\n";
        
        try {
            $pdo = new PDO('mysql:host=localhost;dbname=microservices_db', 'root', '');
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
            $users = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
            $products = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo "  ✅ Database: Connected\n";
            echo "  📊 Users: " . $users['count'] . "\n";
            echo "  📊 Products: " . $products['count'] . "\n";
            
        } catch (PDOException $e) {
            echo "  ❌ Database: Connection failed\n";
            echo "     💡 Run: php setup_xampp.php\n";
        }
        echo "\n";
    }
    
    private function testAuthentication() {
        echo "🔐 Testing Authentication...\n";
        
        // Test Registration
        $email = 'test_' . time() . '@example.com';
        $registerData = [
            'action' => 'register',
            'name' => 'Test User',
            'email' => $email,
            'password' => 'password123'
        ];
        
        $response = $this->makeRequest('POST', $this->services['auth'], $registerData);
        if ($response && $response['status'] === 'success') {
            echo "  ✅ Registration: Success\n";
        } else {
            echo "  ❌ Registration: Failed\n";
            if ($response) {
                echo "     Error: " . ($response['message'] ?? 'Unknown error') . "\n";
            }
        }
        
        // Test Login
        $loginData = [
            'action' => 'login',
            'email' => $email,
            'password' => 'password123'
        ];
        
        $response = $this->makeRequest('POST', $this->services['auth'], $loginData);
        if ($response && $response['status'] === 'success' && isset($response['token'])) {
            $this->token = $response['token'];
            echo "  ✅ Login: Success\n";
            echo "  🔑 Token received\n";
        } else {
            echo "  ❌ Login: Failed\n";
            if ($response) {
                echo "     Error: " . ($response['message'] ?? 'Unknown error') . "\n";
            }
        }
        
        echo "\n";
    }
    
    private function testProductService() {
        echo "🛍️ Testing Product Service...\n";
        
        // Test Get Products (no auth required)
        $response = $this->makeRequest('GET', $this->services['product']);
        if ($response && $response['status'] === 'success') {
            echo "  ✅ Get Products: Success\n";
            echo "  📦 Found " . count($response['data']) . " products\n";
        } else {
            echo "  ❌ Get Products: Failed\n";
        }
        
        // Test Get Single Product
        $response = $this->makeRequest('GET', $this->services['product'] . '?id=1');
        if ($response && $response['status'] === 'success') {
            echo "  ✅ Get Single Product: Success\n";
        } else {
            echo "  ❌ Get Single Product: Failed\n";
        }
        
        echo "\n";
    }
    
    private function testUserService() {
        echo "👤 Testing User Service...\n";
        
        if (!$this->token) {
            echo "  ❌ No token available for user tests\n\n";
            return;
        }
        
        // Test Get User Profile
        $response = $this->makeRequest('GET', $this->services['user'] . '?user_id=1', null, [
            'Authorization: Bearer ' . $this->token
        ]);
        
        if ($response && $response['status'] === 'success') {
            echo "  ✅ Get User Profile: Success\n";
        } else {
            echo "  ❌ Get User Profile: Failed\n";
            if ($response) {
                echo "     Error: " . ($response['message'] ?? 'Unknown error') . "\n";
            }
        }
        
        echo "\n";
    }
    
    private function testOrderService() {
        echo "📦 Testing Order Service...\n";
        
        if (!$this->token) {
            echo "  ❌ No token available for order tests\n\n";
            return;
        }
        
        // Test Create Order
        $orderData = [
            'items' => [
                [
                    'product_id' => 1,
                    'quantity' => 2
                ]
            ],
            'shipping_address' => 'Test Address, Test City',
            'payment_method' => 'cash'
        ];
        
        $response = $this->makeRequest('POST', $this->services['order'], $orderData, [
            'Authorization: Bearer ' . $this->token
        ]);
        
        if ($response && $response['status'] === 'success') {
            echo "  ✅ Create Order: Success\n";
            echo "  📋 Order ID: " . $response['order_id'] . "\n";
        } else {
            echo "  ❌ Create Order: Failed\n";
            if ($response) {
                echo "     Error: " . ($response['message'] ?? 'Unknown error') . "\n";
            }
        }
        
        echo "\n";
    }
    
    private function makeRequest($method, $url, $data = null, $headers = []) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        
        $defaultHeaders = ['Content-Type: application/json'];
        $allHeaders = array_merge($defaultHeaders, $headers);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $allHeaders);
        
        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "  ⚠️ CURL Error: $error\n";
            return null;
        }
        
        if ($httpCode >= 200 && $httpCode < 300) {
            return json_decode($response, true);
        }
        
        echo "  ⚠️ HTTP Error: $httpCode\n";
        return null;
    }
}

// Run tests
echo "🔧 XAMPP Microservices API Tester\n";
echo "==================================\n\n";

$tester = new XAMPPAPITester();
$tester->runAllTests();

echo "\n💡 Tips:\n";
echo "- If services are not running, execute: xampp_setup.bat\n";
echo "- If database errors occur, run: php setup_xampp.php\n";
echo "- Check XAMPP Control Panel for MySQL status\n";
