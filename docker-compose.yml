version: '3'
services:
  auth-service:
    build: ./auth-service
    ports:
      - "8001:80"
    volumes:
      - ./auth-service:/var/www/html
    depends_on:
      - db
    environment:
      - MYSQL_HOST=db
      - MYSQL_USER=root
      - MYSQL_PASSWORD=root
      - MYSQL_DATABASE=microservices_db
  user-service:
    build: ./user-service
    ports:
      - "8002:80"
    volumes:
      - ./user-service:/var/www/html
    depends_on:
      - db
    environment:
      - MYSQL_HOST=db
      - MYSQL_USER=root
      - MYSQL_PASSWORD=root
      - MYSQL_DATABASE=microservices_db

  product-service:
    build: ./product-service
    ports:
      - "8003:80"
    volumes:
      - ./product-service:/var/www/html
    depends_on:
      - db
  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: microservices_db
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql

volumes:
  db_data:
