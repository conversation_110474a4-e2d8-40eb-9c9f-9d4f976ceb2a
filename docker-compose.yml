version: '3.8'

networks:
  microservices-network:
    driver: bridge

services:
  # API Gateway
  api-gateway:
    build: ./api-gateway
    ports:
      - "8000:80"
    volumes:
      - ./api-gateway:/var/www/html
      - ./shared:/var/www/html/shared
    depends_on:
      - auth-service
      - user-service
      - product-service
      - order-service
      - redis
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    networks:
      - microservices-network

  # Authentication Service
  auth-service:
    build: ./auth-service
    ports:
      - "8001:80"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./auth-service:/var/www/html
      - ./shared:/var/www/html/shared
    depends_on:
      - db
    environment:
      - MYSQL_HOST=db
      - MYSQL_USER=root
      - MYSQL_PASSWORD=root
      - MYSQL_DATABASE=microservices_db
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
    networks:
      - microservices-network

  # User Service
  user-service:
    build: ./user-service
    ports:
      - "8002:80"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./user-service:/var/www/html
      - ./shared:/var/www/html/shared
    depends_on:
      - db
      - auth-service
    environment:
      - MYSQL_HOST=db
      - MYSQL_USER=root
      - MYSQL_PASSWORD=root
      - MYSQL_DATABASE=microservices_db
      - AUTH_SERVICE_URL=http://auth-service
    networks:
      - microservices-network

  # Product Service
  product-service:
    build: ./product-service
    ports:
      - "8003:80"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./product-service:/var/www/html
      - ./shared:/var/www/html/shared
    depends_on:
      - db
      - auth-service
    environment:
      - MYSQL_HOST=db
      - MYSQL_USER=root
      - MYSQL_PASSWORD=root
      - MYSQL_DATABASE=microservices_db
      - AUTH_SERVICE_URL=http://auth-service
    networks:
      - microservices-network

  # Order Service
  order-service:
    build: ./order-service
    ports:
      - "8004:80"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./order-service:/var/www/html
      - ./shared:/var/www/html/shared
    depends_on:
      - db
      - auth-service
      - product-service
    environment:
      - MYSQL_HOST=db
      - MYSQL_USER=root
      - MYSQL_PASSWORD=root
      - MYSQL_DATABASE=microservices_db
      - AUTH_SERVICE_URL=http://auth-service
      - PRODUCT_SERVICE_URL=http://product-service
      - EMAIL_NOTIFICATIONS=true
      - SMS_NOTIFICATIONS=false
    networks:
      - microservices-network

  # Database
  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: microservices_db
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - microservices-network

  # Redis for caching and rate limiting
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - microservices-network

volumes:
  db_data:
  redis_data:
