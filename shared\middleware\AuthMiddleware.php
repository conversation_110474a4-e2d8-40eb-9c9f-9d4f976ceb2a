<?php
class AuthMiddleware {
    private $authServiceUrl;
    
    public function __construct() {
        $this->authServiceUrl = getenv('AUTH_SERVICE_URL') ?: 'http://auth-service';
    }
    
    public function verifyToken($token) {
        // Remove Bearer prefix if present
        $token = str_replace('Bearer ', '', $token);
        
        // Call auth service to verify token
        $data = json_encode(['action' => 'verify', 'token' => $token]);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->authServiceUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            $result = json_decode($response, true);
            if ($result && $result['status'] === 'success' && $result['valid']) {
                return $result['user_id'];
            }
        }
        
        return false;
    }
    
    public function requireAuth() {
        $headers = getallheaders();
        
        if (!isset($headers['Authorization'])) {
            http_response_code(401);
            echo json_encode(['status' => 'error', 'message' => 'Authorization header required']);
            exit();
        }
        
        $userId = $this->verifyToken($headers['Authorization']);
        
        if (!$userId) {
            http_response_code(401);
            echo json_encode(['status' => 'error', 'message' => 'Invalid or expired token']);
            exit();
        }
        
        return $userId;
    }
    
    public function optionalAuth() {
        $headers = getallheaders();
        
        if (isset($headers['Authorization'])) {
            return $this->verifyToken($headers['Authorization']);
        }
        
        return null;
    }
}
